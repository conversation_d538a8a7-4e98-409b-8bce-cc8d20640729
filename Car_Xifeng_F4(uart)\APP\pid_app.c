#include "pid_app.h"

/* PID 控制器实例 */
PID_T pid_speed_x;  // 左轮速度环
PID_T pid_speed_y; // 右轮速度环
int targe_error=0;
PidParams_t pid_params_X = {
    .kp = 2.1f,  // Calculated for balanced response to steps like 88 to 149
    .ki = 0.00f, // Lowered to minimize integral windup during long settles
    .kd = 5.3f,  // For controlling oscillations in transitions
    .out_min = -100.0f,
    .out_max = 100.0f
};

// Refinement for faster response and reduced oscillations: boost kp/ki slightly for quicker settling, keep high kd for damping.
// Addresses slow transitions in log by increasing drive; lowers risk of windup with moderate ki.
// Tweak for better large-step response and reduced settling time: increase kp/ki for drive on big changes, boost kd for bounce control.
// Based on log's slow upward steps and holds; aims to speed up without worsening oscillations.
// <PERSON><PERSON><PERSON>-<PERSON> tuned params from log analysis: moderate kp for response, low ki to avoid windup, kd for damping.
// Reduces long holds and bounces seen in log.
PidParams_t pid_params_Y = {
    .kp = 2.1f,  // Calculated for balanced response to steps like 88 to 149
    .ki = 0.0f, // Lowered to minimize integral windup during long settles
    .kd = 5.3f,  // For controlling oscillations in transitions
    .out_min = -100.0f,
    .out_max = 100.0f
};

void PID_Init(void)
{
  pid_init(&pid_speed_x,
           pid_params_X.kp, pid_params_X.ki, pid_params_X.kd,
           0.0f, pid_params_X.out_max);

  pid_init(&pid_speed_y,
           pid_params_Y.kp, pid_params_Y.ki, pid_params_Y.kd,
           0.0f, pid_params_Y.out_max);

  
  pid_set_target(&pid_speed_x, 0);
  pid_set_target(&pid_speed_y, 0);
}
int error_x;//latest_green_laser_coord.x-latest_red_laser_coord.x
int error_y;//latest_green_laser_coord.y-latest_red_laser_coord.y
bool pid_running = true; // PID 控制使能开关
//int output_x, output_y;
//void PID_Task(void)
//{
//	error_x=latest_green_laser_coord.x-latest_red_laser_coord.x;
//	error_y=latest_green_laser_coord.y-latest_red_laser_coord.y;
//    if(pid_running == false) return;
//		
//    
////    output_left = pid_calculate_incremental(&pid_speed_left, filtered_speed_left);
////    output_right = pid_calculate_incremental(&pid_speed_right, filtered_speed_right);
//	   output_x = pid_calculate_positional(&pid_speed_x, error_x);
//     output_y = pid_calculate_positional(&pid_speed_y, error_y);
////		//output_yaw =  pid_calculate_incremental(&pid_yaw, yaw); -> PID计算 YAW -> 反馈

//		//pid_set_target(&pid_speed_right,v - output_yaw);
//		   output_x = pid_constrain(output_x, pid_params_X.out_min,pid_params_X.out_max);
//    output_y = pid_constrain(output_y, pid_params_Y.out_min,pid_params_Y.out_max);

////    // 设置电机速度
////    Motor_SetSpeed(&left_motor, output_left);
////    Motor_SetSpeed(&right_motor, output_right);
//	StepMotor_Set_Speed(output_x,-output_y);
//    my_printf(&huart1,"{targe}%d,%d\r\n", latest_green_laser_coord.x, latest_red_laser_coord.x);
////	my_printf(&huart1,"{me}%d,%d\r\n", error_y, error_x);
////	my_printf(&huart1,"{me1}%d\r\n", output_y);
////		my_printf(&huart1,"{right_filtered}%.2f,%.2f\r\n", pid_speed_right.target, filtered_speed_right);
//}


