/**
 * @file y_axis_homing_test.c
 * @brief Y轴零点回归功能测试示例
 * @copyright 米醋电子工作室
 */

#include "MyDefine.h"
#include "StepMotor_app.h"

/**
 * @brief Y轴回零功能测试
 * @note 此函数演示如何使用Y轴回零功能
 */
void Test_Y_Axis_Homing(void)
{
    my_printf(&huart1, "\r\n========== Y轴回零功能测试 ==========\r\n");
    
    // 测试1: 自动回零
    my_printf(&huart1, "测试1: 执行Y轴自动回零\r\n");
    uint8_t result = StepMotor_Y_Auto_Homing(15000); // 15秒超时
    
    switch(result)
    {
        case 1:
            my_printf(&huart1, "✓ Y轴自动回零成功！\r\n");
            break;
        case 0:
            my_printf(&huart1, "✗ Y轴回零超时，尝试限位开关回零\r\n");
            StepMotor_Y_Homing_With_Limit_Switch();
            HAL_Delay(5000); // 等待5秒
            break;
        case 2:
            my_printf(&huart1, "✗ Y轴回零通信错误\r\n");
            return;
    }
    
    // 测试2: 移动测试
    my_printf(&huart1, "\r\n测试2: Y轴移动测试\r\n");
    my_printf(&huart1, "向前移动1000脉冲...\r\n");
    result = StepMotor_Move_Pulses_Wait(0, 1000, 10000);
    if (result == 1) {
        my_printf(&huart1, "✓ Y轴移动完成\r\n");
        StepMotor_Print_Pulse_Status();
    }
    
    HAL_Delay(2000);
    
    // 测试3: 返回零点
    my_printf(&huart1, "\r\n测试3: 返回零点\r\n");
    int32_t current_y = StepMotor_Get_Pulse_Count(2);
    my_printf(&huart1, "当前Y轴位置: %ld 脉冲\r\n", current_y);
    
    if (current_y != 0) {
        my_printf(&huart1, "返回零点...\r\n");
        result = StepMotor_Move_Pulses_Wait(0, -current_y, 10000);
        if (result == 1) {
            my_printf(&huart1, "✓ 已返回零点\r\n");
            StepMotor_Print_Pulse_Status();
        }
    }
    
    // 测试4: 重新设置零点
    my_printf(&huart1, "\r\n测试4: 重新设置零点\r\n");
    my_printf(&huart1, "移动到新位置...\r\n");
    result = StepMotor_Move_Pulses_Wait(0, 500, 10000);
    if (result == 1) {
        my_printf(&huart1, "设置当前位置为新零点...\r\n");
        StepMotor_Y_Set_Zero_Point();
        StepMotor_Print_Pulse_Status();
    }
    
    my_printf(&huart1, "\r\n========== Y轴回零测试完成 ==========\r\n");
}

/**
 * @brief Y轴回零状态监控测试
 * @note 此函数演示如何监控回零过程
 */
void Test_Y_Axis_Homing_Monitor(void)
{
    my_printf(&huart1, "\r\n========== Y轴回零状态监控测试 ==========\r\n");
    
    // 启动回零过程（非阻塞）
    my_printf(&huart1, "启动Y轴回零过程...\r\n");
    StepMotor_Y_Homing_With_Limit_Switch();
    
    // 监控回零状态
    uint32_t start_time = HAL_GetTick();
    uint32_t timeout = 20000; // 20秒超时
    
    while (HAL_GetTick() - start_time < timeout)
    {
        uint8_t status = StepMotor_Y_Check_Homing_Status();
        
        switch(status)
        {
            case 0:
                my_printf(&huart1, "回零中... (已用时: %lu ms)\r\n", HAL_GetTick() - start_time);
                break;
            case 1:
                my_printf(&huart1, "✓ 回零完成！(用时: %lu ms)\r\n", HAL_GetTick() - start_time);
                StepMotor_Print_Pulse_Status();
                return;
            case 2:
                my_printf(&huart1, "✗ 回零失败！\r\n");
                return;
        }
        
        HAL_Delay(1000); // 每秒检查一次
    }
    
    my_printf(&huart1, "✗ 回零监控超时！\r\n");
}

/**
 * @brief Y轴回零参数测试
 * @note 此函数演示不同参数下的回零效果
 */
void Test_Y_Axis_Homing_Parameters(void)
{
    my_printf(&huart1, "\r\n========== Y轴回零参数测试 ==========\r\n");
    
    // 测试不同的回零速度
    my_printf(&huart1, "测试1: 快速回零 (3 RPM)\r\n");
    
    // 手动配置回零参数 - 快速模式
    Emm_V5_Origin_Modify_Params(&MOTOR_Y_UART, MOTOR_Y_ADDR, true, 
                                0,      // o_mode: 单圈接近开关
                                1,      // o_dir: CCW方向
                                3,      // o_vel: 3RPM快速
                                10000,  // o_tm: 10秒超时
                                1,      // sl_vel: 1RPM慢速接近
                                50,     // sl_ma: 50mA堵转检测
                                1000,   // sl_ms: 1000ms堵转时间
                                false); // potF: 不使用电位器
    HAL_Delay(50);
    
    // 触发回零
    Emm_V5_Origin_Trigger_Return(&MOTOR_Y_UART, MOTOR_Y_ADDR, 0, false);
    
    // 等待完成
    uint32_t start_time = HAL_GetTick();
    while (HAL_GetTick() - start_time < 15000) // 15秒超时
    {
        if (StepMotor_Check_Ready() == 1) {
            my_printf(&huart1, "✓ 快速回零完成！(用时: %lu ms)\r\n", HAL_GetTick() - start_time);
            break;
        }
        HAL_Delay(100);
    }
    
    HAL_Delay(2000);
    
    // 移动到其他位置准备下一次测试
    StepMotor_Move_Pulses_Wait(0, 800, 10000);
    
    my_printf(&huart1, "\r\n测试2: 慢速精确回零 (1 RPM)\r\n");
    
    // 手动配置回零参数 - 慢速精确模式
    Emm_V5_Origin_Modify_Params(&MOTOR_Y_UART, MOTOR_Y_ADDR, true,
                                0,      // o_mode: 单圈接近开关
                                1,      // o_dir: CCW方向  
                                1,      // o_vel: 1RPM慢速
                                15000,  // o_tm: 15秒超时
                                1,      // sl_vel: 1RPM慢速接近
                                30,     // sl_ma: 30mA更敏感的堵转检测
                                800,    // sl_ms: 800ms堵转时间
                                false); // potF: 不使用电位器
    HAL_Delay(50);
    
    // 触发回零
    Emm_V5_Origin_Trigger_Return(&MOTOR_Y_UART, MOTOR_Y_ADDR, 0, false);
    
    // 等待完成
    start_time = HAL_GetTick();
    while (HAL_GetTick() - start_time < 20000) // 20秒超时
    {
        if (StepMotor_Check_Ready() == 1) {
            my_printf(&huart1, "✓ 慢速精确回零完成！(用时: %lu ms)\r\n", HAL_GetTick() - start_time);
            break;
        }
        HAL_Delay(100);
    }
    
    my_printf(&huart1, "\r\n========== 参数测试完成 ==========\r\n");
}

/**
 * @brief 完整的Y轴回零功能测试套件
 * @note 在main函数中调用此函数进行完整测试
 */
void Run_Y_Axis_Homing_Test_Suite(void)
{
    my_printf(&huart1, "\r\n\r\n");
    my_printf(&huart1, "================================================\r\n");
    my_printf(&huart1, "           Y轴零点回归功能测试套件\r\n");
    my_printf(&huart1, "================================================\r\n");
    
    // 基础功能测试
    Test_Y_Axis_Homing();
    HAL_Delay(3000);
    
    // 状态监控测试
    Test_Y_Axis_Homing_Monitor();
    HAL_Delay(3000);
    
    // 参数测试
    Test_Y_Axis_Homing_Parameters();
    
    my_printf(&huart1, "\r\n================================================\r\n");
    my_printf(&huart1, "           测试套件执行完成\r\n");
    my_printf(&huart1, "================================================\r\n");
}
