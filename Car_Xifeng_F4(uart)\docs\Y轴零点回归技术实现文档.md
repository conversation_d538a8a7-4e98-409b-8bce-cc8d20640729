# Y轴零点回归技术实现文档

## 概述

本文档详细说明Y轴步进电机零点回归功能的技术实现，基于Emm_V5.0步进闭环驱动器的原点回归功能。

## 技术架构

### 1. 核心组件

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   主控MCU       │    │  Emm_V5驱动器   │    │   Y轴步进电机   │
│  (STM32F4)      │◄──►│                 │◄──►│                 │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                       ▲                       ▲
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   串口通信      │    │   原点检测      │    │   机械限位      │
│  (UART4)        │    │  (接近开关)     │    │  (限位开关)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. 软件架构

```
应用层 (Application Layer)
├── StepMotor_Y_Auto_Homing()          // 自动回零主函数
├── StepMotor_Y_Set_Zero_Point()       // 零点设置函数
├── StepMotor_Y_Homing_With_Limit_Switch() // 限位开关回零
└── StepMotor_Y_Check_Homing_Status()  // 状态检查函数

驱动层 (Driver Layer)  
├── Emm_V5_Origin_Set_O()              // 设置原点
├── Emm_V5_Origin_Modify_Params()      // 修改回零参数
├── Emm_V5_Origin_Trigger_Return()     // 触发回零运动
└── Emm_V5_Origin_Interrupt()          // 中断回零

硬件抽象层 (HAL Layer)
├── HAL_UART_Transmit()                // 串口发送
├── HAL_UART_Receive_IT()              // 串口中断接收
└── HAL_Delay()                        // 延时函数
```

## 实现细节

### 1. 回零模式选择

#### 模式0: 单圈接近开关模式
```c
// 参数配置
o_mode = 0;          // 单圈接近开关
o_dir = 1;           // CCW方向回零
o_vel = 1;           // 回零速度1RPM
sl_vel = 1;          // 慢速接近1RPM
```

**工作原理**:
1. 电机以设定速度向回零方向运动
2. 检测到接近开关信号后，切换到慢速模式
3. 慢速接近直到精确定位
4. 停止运动并设置当前位置为零点

#### 模式2: 单圈堵转检测模式
```c
// 参数配置  
o_mode = 2;          // 堵转检测模式
sl_ma = 100;         // 堵转电流100mA
sl_ms = 500;         // 堵转时间500ms
```

**工作原理**:
1. 电机向回零方向运动
2. 遇到机械限位时电流增大
3. 检测到堵转电流超过阈值且持续指定时间
4. 停止运动并设置当前位置为零点

### 2. 通信协议

#### 命令格式
```
发送: [地址][功能码][参数1][参数2]...[校验]
接收: [0x01][0xFD][0x9F]...[0x6B]
```

#### 关键命令
- **设置原点**: `0x93` 功能码
- **修改参数**: `0x4C` 功能码  
- **触发回零**: `0x91` 功能码
- **读取状态**: `0x3A` 功能码

### 3. 状态机实现

```c
typedef enum {
    HOMING_IDLE = 0,        // 空闲状态
    HOMING_INIT,            // 初始化状态
    HOMING_RUNNING,         // 回零运行中
    HOMING_COMPLETE,        // 回零完成
    HOMING_ERROR,           // 回零错误
    HOMING_TIMEOUT          // 回零超时
} HomingState_t;
```

### 4. 错误处理机制

#### 超时处理
```c
uint32_t start_time = HAL_GetTick();
while (HAL_GetTick() - start_time < timeout_ms) {
    // 检查状态
    if (completed) return SUCCESS;
    if (error) return ERROR;
    HAL_Delay(10);
}
return TIMEOUT;
```

#### 通信错误处理
```c
if (stop_flag_car == 2) {
    my_printf(&huart1, "通信错误，重试连接\r\n");
    // 重新初始化通信
    StepMotor_Init_Encoder();
    return COMM_ERROR;
}
```

## 关键算法

### 1. 脉冲计数管理

```c
// 回零完成后重置计数
void Reset_Pulse_Count_After_Homing(void) {
    motor_y_pulse_count = 0;      // 当前位置清零
    motor_y_target_pulses = 0;    // 目标位置清零
}
```

### 2. 位置同步算法

```c
// 确保软件计数与硬件位置同步
void Sync_Position_After_Homing(void) {
    // 读取驱动器实际位置
    Emm_V5_Read_Sys_Params(&MOTOR_Y_UART, MOTOR_Y_ADDR, S_CPOS);
    
    // 同步软件计数
    motor_y_pulse_count = 0;  // 零点位置
}
```

### 3. 自适应参数调整

```c
// 根据负载自动调整堵转检测参数
void Auto_Adjust_Stall_Parameters(void) {
    if (load_heavy) {
        stall_current = 150;  // 重负载提高阈值
        stall_time = 800;     // 缩短检测时间
    } else {
        stall_current = 50;   // 轻负载降低阈值  
        stall_time = 1000;    // 延长检测时间
    }
}
```

## 性能优化

### 1. 回零速度优化

```c
// 两段式回零速度
Phase1: 快速接近 (3 RPM) -> 检测到信号
Phase2: 慢速精确 (1 RPM) -> 精确定位
```

### 2. 通信优化

```c
// 批量参数设置，减少通信次数
void Batch_Set_Homing_Params(void) {
    Emm_V5_Origin_Modify_Params(...);  // 一次设置所有参数
    HAL_Delay(50);                     // 等待参数生效
    Emm_V5_Origin_Trigger_Return(...); // 触发回零
}
```

### 3. 内存优化

```c
// 使用位域节省内存
typedef struct {
    uint8_t homing_complete : 1;
    uint8_t homing_error : 1;
    uint8_t comm_error : 1;
    uint8_t timeout_error : 1;
    uint8_t reserved : 4;
} HomingFlags_t;
```

## 安全机制

### 1. 硬件安全
- 机械限位开关保护
- 电机堵转保护
- 过流保护

### 2. 软件安全
- 超时保护机制
- 通信错误检测
- 状态机异常处理

### 3. 用户安全
- 回零过程状态提示
- 错误信息详细反馈
- 紧急停止功能

## 调试与测试

### 1. 调试信息输出
```c
#define DEBUG_HOMING 1
#if DEBUG_HOMING
    my_printf(&huart1, "Homing State: %d\r\n", state);
    my_printf(&huart1, "Current Pos: %ld\r\n", position);
#endif
```

### 2. 测试用例
- 正常回零测试
- 超时测试
- 通信错误测试
- 机械故障测试

### 3. 性能测试
- 回零精度测试
- 回零速度测试
- 重复性测试

## 维护与升级

### 1. 参数调优
根据实际使用情况调整:
- 回零速度
- 堵转检测阈值
- 超时时间

### 2. 功能扩展
- 多轴同步回零
- 自学习零点位置
- 远程回零控制

### 3. 兼容性
- 支持不同型号驱动器
- 支持不同类型传感器
- 支持不同通信协议

## 版权信息

本技术文档由米醋电子工作室编写，基于Emm_V5.0步进闭环驱动器技术实现。
