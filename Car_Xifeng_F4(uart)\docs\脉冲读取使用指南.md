# 脉冲读取使用指南

## 🎯 功能概述

现在你可以实时读取X轴和Y轴的脉冲计数，包括当前脉冲数和目标脉冲数。

## 📊 可用的脉冲读取API

### 1. 读取单个电机脉冲计数
```c
// 读取X轴当前脉冲计数
int32_t x_pulses = StepMotor_Get_Pulse_Count(1);

// 读取Y轴当前脉冲计数  
int32_t y_pulses = StepMotor_Get_Pulse_Count(2);
```

### 2. 读取目标脉冲数
```c
// 读取X轴目标脉冲数
int32_t x_target = StepMotor_Get_Target_Pulses(1);

// 读取Y轴目标脉冲数
int32_t y_target = StepMotor_Get_Target_Pulses(2);
```

### 3. 批量读取所有脉冲信息
```c
int32_t x_current, y_current, x_target, y_target;
StepMotor_Get_All_Pulse_Info(&x_current, &y_current, &x_target, &y_target);

my_printf(&huart1, "X: %ld/%ld, Y: %ld/%ld\r\n", 
          x_current, x_target, y_current, y_target);
```

### 4. 打印脉冲状态
```c
// 直接打印所有脉冲信息
StepMotor_Print_Pulse_Status();
```

### 5. 重置脉冲计数
```c
StepMotor_Reset_Pulse_Count(1);  // 重置X轴
StepMotor_Reset_Pulse_Count(2);  // 重置Y轴  
StepMotor_Reset_Pulse_Count(0);  // 重置所有轴
```

## 💡 使用示例

### 示例1: 监控电机运动进度
```c
void monitor_motor_progress(void)
{
    // 开始运动
    StepMotor_Move_Pulses(1000, 500);
    
    // 监控进度
    while(1) {
        int32_t x_current = StepMotor_Get_Pulse_Count(1);
        int32_t y_current = StepMotor_Get_Pulse_Count(2);
        
        my_printf(&huart1, "Progress: X=%ld Y=%ld\r\n", x_current, y_current);
        
        // 检查是否到位
        if(StepMotor_Check_Ready() == 1) {
            my_printf(&huart1, "Motion Complete!\r\n");
            break;
        }
        
        HAL_Delay(100);
    }
}
```

### 示例2: 位置反馈控制
```c
void position_feedback_control(void)
{
    // 目标位置
    int32_t target_x = 2000;
    int32_t target_y = 1500;
    
    // 移动到目标位置
    StepMotor_Move_Pulses(target_x, target_y);
    
    // 等待到位并显示最终位置
    StepMotor_Move_Pulses_Wait(target_x, target_y, 10000);
    
    // 显示最终位置
    int32_t final_x = StepMotor_Get_Pulse_Count(1);
    int32_t final_y = StepMotor_Get_Pulse_Count(2);
    
    my_printf(&huart1, "Final Position: X=%ld Y=%ld\r\n", final_x, final_y);
}
```

### 示例3: 相对运动控制
```c
void relative_motion_control(void)
{
    // 获取当前位置
    int32_t current_x = StepMotor_Get_Pulse_Count(1);
    int32_t current_y = StepMotor_Get_Pulse_Count(2);
    
    my_printf(&huart1, "Current: X=%ld Y=%ld\r\n", current_x, current_y);
    
    // 相对运动 +500 脉冲
    StepMotor_Move_Pulses(500, 500);
    
    // 等待完成
    HAL_Delay(2000);
    
    // 显示新位置
    int32_t new_x = StepMotor_Get_Pulse_Count(1);
    int32_t new_y = StepMotor_Get_Pulse_Count(2);
    
    my_printf(&huart1, "New: X=%ld Y=%ld\r\n", new_x, new_y);
    my_printf(&huart1, "Delta: X=%ld Y=%ld\r\n", new_x-current_x, new_y-current_y);
}
```

## 📈 脉冲计数原理

1. **累加计数**: 每次调用`StepMotor_Move_Pulses()`时，脉冲数会累加到当前计数中
2. **方向处理**: 正数脉冲增加计数，负数脉冲减少计数
3. **实时更新**: 脉冲计数在电机运动命令发出时立即更新
4. **持久保存**: 脉冲计数会一直保持，直到手动重置或系统重启

## ⚠️ 注意事项

1. **脉冲计数是软件累加的**，不是从编码器硬件读取的实时位置
2. **断电会丢失计数**，重启后需要重新校准零点
3. **步进电机失步时**，软件计数与实际位置可能不一致
4. **建议定期校准**，通过限位开关或其他方式确认实际位置

## 🔧 调试输出格式

所有调试输出都使用英文格式，便于串口监控：
- `X_Pulse: Current=1000 Target=1000`
- `Y_Pulse: Current=500 Target=500`
- `Pulse Reset: Motor_1`
- `Progress: X=750 Y=300`
