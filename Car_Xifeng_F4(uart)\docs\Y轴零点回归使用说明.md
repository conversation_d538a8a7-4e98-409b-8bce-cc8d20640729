# Y轴步进电机零点回归功能使用说明

## 功能概述

本文档说明如何使用新增的Y轴步进电机零点回归功能。该功能基于Emm_V5.0步进闭环驱动器实现，支持自动回零和手动零点设置。

## 主要功能

### 1. 自动上电回零
- 系统上电时，Y轴电机会自动执行回零操作
- 使用接近开关或限位开关检测零点位置
- 回零完成后，脉冲计数自动重置为0

### 2. 手动零点设置
- 可以将当前位置设置为零点
- 适用于需要重新定义零点位置的场景

### 3. 状态监控
- 实时监控回零过程状态
- 提供超时保护和错误处理

## API函数说明

### StepMotor_Y_Auto_Homing()
```c
uint8_t StepMotor_Y_Auto_Homing(uint32_t timeout_ms);
```
**功能**: Y轴自动回零
**参数**: 
- `timeout_ms`: 回零超时时间（毫秒）
**返回值**: 
- 0: 超时
- 1: 回零成功  
- 2: 通信错误

**使用示例**:
```c
// 执行Y轴回零，超时时间20秒
uint8_t result = StepMotor_Y_Auto_Homing(20000);
if (result == 1) {
    printf("Y轴回零成功！\n");
}
```

### StepMotor_Y_Set_Zero_Point()
```c
void StepMotor_Y_Set_Zero_Point(void);
```
**功能**: 设置Y轴当前位置为零点
**使用示例**:
```c
// 将当前位置设为零点
StepMotor_Y_Set_Zero_Point();
```

### StepMotor_Y_Homing_With_Limit_Switch()
```c
void StepMotor_Y_Homing_With_Limit_Switch(void);
```
**功能**: 使用限位开关进行Y轴回零（备用方案）
**使用示例**:
```c
// 使用限位开关回零
StepMotor_Y_Homing_With_Limit_Switch();
```

### StepMotor_Y_Check_Homing_Status()
```c
uint8_t StepMotor_Y_Check_Homing_Status(void);
```
**功能**: 检查Y轴回零状态
**返回值**:
- 0: 回零中
- 1: 回零完成
- 2: 回零失败

## 配置参数

在 `MyDefine.h` 中定义了以下可配置参数：

```c
#define Y_HOMING_TIMEOUT_MS     20000    // Y轴回零超时时间(毫秒)
#define Y_HOMING_SPEED_RPM      1        // Y轴回零速度(RPM)
#define Y_HOMING_SLOW_SPEED_RPM 1        // Y轴慢速接近速度(RPM)
#define Y_HOMING_STALL_CURRENT  50       // Y轴堵转检测电流(mA)
#define Y_HOMING_STALL_TIME_MS  1000     // Y轴堵转检测时间(ms)
```

## 回零模式说明

### 模式0: 单圈接近开关模式
- 适用于安装了接近开关的机械结构
- 检测到接近开关信号后停止并设为零点
- 精度高，推荐使用

### 模式2: 单圈堵转检测模式  
- 适用于使用限位开关的机械结构
- 检测到堵转电流后停止并设为零点
- 作为备用方案使用

## 使用流程

### 1. 系统初始化时自动回零
```c
void main(void) {
    // 系统初始化
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_USART_Init();
    
    // 步进电机初始化（包含自动回零）
    StepMotor_Init();  // 会自动执行Y轴回零
    
    // 其他初始化...
}
```

### 2. 运行时手动回零
```c
// 在需要重新回零时调用
uint8_t result = StepMotor_Y_Auto_Homing(15000);
switch(result) {
    case 1:
        printf("回零成功\n");
        break;
    case 0:
        printf("回零超时\n");
        break;
    case 2:
        printf("通信错误\n");
        break;
}
```

### 3. 设置自定义零点
```c
// 移动到期望的零点位置
StepMotor_Move_Pulses(0, 1000);  // 移动到某个位置
StepMotor_Move_Pulses_Wait(0, 0, 5000);  // 等待到位

// 将当前位置设为零点
StepMotor_Y_Set_Zero_Point();
```

## 注意事项

1. **机械结构要求**:
   - 确保Y轴安装了接近开关或限位开关
   - 开关位置应设置在期望的零点位置

2. **电气连接**:
   - 确保接近开关正确连接到驱动器
   - 检查驱动器地址设置正确

3. **参数调整**:
   - 根据实际机械结构调整回零速度
   - 根据负载情况调整堵转检测电流

4. **安全考虑**:
   - 回零过程中避免手动干预
   - 确保回零路径上无障碍物

## 故障排除

### 回零超时
- 检查接近开关是否正常工作
- 检查机械结构是否卡死
- 调整回零速度和超时时间

### 通信错误
- 检查串口连接
- 检查驱动器地址设置
- 检查波特率配置

### 回零位置不准确
- 检查接近开关安装位置
- 调整慢速接近速度
- 检查机械间隙

## 版权信息

本功能由米醋电子工作室开发，基于Emm_V5.0步进闭环驱动器实现。
