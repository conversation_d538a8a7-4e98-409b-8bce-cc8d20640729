Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) for DMA1_Stream0_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) for DMA1_Stream2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) for DMA1_Stream5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UART4_IRQHandler) for UART4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UART5_IRQHandler) for UART5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) for DMA2_Stream1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART6_IRQHandler) for USART6_IRQHandler
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SaveCoordinatesToFlash) refers to main.o(i.WriteFlashData) for WriteFlashData
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.WriteFlashData) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    main.o(i.WriteFlashData) refers to main.o(i.STMFLASH_GetFlashSector) for STMFLASH_GetFlashSector
    main.o(i.WriteFlashData) refers to stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) for HAL_FLASHEx_Erase
    main.o(i.WriteFlashData) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    main.o(i.WriteFlashData) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) for HAL_FLASH_Program
    main.o(i.WriteFlashData) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C2_Init) for MX_I2C2_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to usart.o(i.MX_UART4_Init) for MX_UART4_Init
    main.o(i.main) refers to usart.o(i.MX_USART6_UART_Init) for MX_USART6_UART_Init
    main.o(i.main) refers to usart.o(i.MX_UART5_Init) for MX_UART5_Init
    main.o(i.main) refers to spi.o(i.MX_SPI1_Init) for MX_SPI1_Init
    main.o(i.main) refers to main.o(i.ReadCoordinatesFromFlash) for ReadCoordinatesFromFlash
    main.o(i.main) refers to usart.o(i.my_printf) for my_printf
    main.o(i.main) refers to scheduler.o(i.Scheduler_Init) for Scheduler_Init
    main.o(i.main) refers to scheduler.o(i.Scheduler_Run) for Scheduler_Run
    main.o(i.main) refers to main.o(.data) for .data
    main.o(i.main) refers to usart.o(.bss) for huart1
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C2_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C2_Init) refers to i2c.o(.bss) for .bss
    spi.o(i.HAL_SPI_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    spi.o(i.HAL_SPI_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    spi.o(i.HAL_SPI_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    spi.o(i.MX_SPI1_Init) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(i.MX_SPI1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    spi.o(i.MX_SPI1_Init) refers to spi.o(.bss) for .bss
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_MspPostInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM1_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM4_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.HAL_UART_RxCpltCallback) refers to stepmotor_app.o(i.Motor_X_Receive_Data) for Motor_X_Receive_Data
    usart.o(i.HAL_UART_RxCpltCallback) refers to stepmotor_app.o(i.Motor_Y_Receive_Data) for Motor_Y_Receive_Data
    usart.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart.o(i.HAL_UART_RxCpltCallback) refers to stepmotor_app.o(.data) for motor_x_rx_byte
    usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for .bss
    usart.o(i.MX_UART4_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART4_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_UART5_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART5_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART5_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART6_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART6_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART6_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    usart.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to usart.o(.bss) for hdma_uart5_rx
    stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_uart4_rx
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to usart.o(.bss) for hdma_usart2_rx
    stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) refers to usart.o(.bss) for hdma_usart6_rx
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.UART4_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.UART4_IRQHandler) refers to usart.o(.bss) for huart4
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to usart.o(.bss) for huart5
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f4xx_it.o(i.USART6_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART6_IRQHandler) refers to usart.o(.bss) for huart6
    stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit) refers to spi.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Init) refers to spi.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAError) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    motor_driver.o(i.Motor_Create) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    motor_driver.o(i.Motor_Create) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver.o(i.Motor_Enable) refers to motor_driver.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(i.Motor_Enable) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver.o(i.Motor_GetState) refers to motor_driver.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(i.Motor_SetSpeed) refers to motor_driver.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(i.Motor_SetSpeed) refers to motor_driver.o(i.Speed_To_PWM) for Speed_To_PWM
    motor_driver.o(i.Motor_SetSpeed) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver.o(i.Motor_Stop) refers to motor_driver.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(i.Motor_Stop) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    hwt101_driver.o(i.HWT101_Enable) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_GetData) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_GetGyroZ) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_GetState) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_GetYaw) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_ProcessBuffer) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_ProcessBuffer) refers to hwt101_driver.o(i.HWT101_ConvertGyroData) for HWT101_ConvertGyroData
    hwt101_driver.o(i.HWT101_ProcessBuffer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    hwt101_driver.o(i.HWT101_ResetYaw) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_ResetYaw) refers to hwt101_driver.o(i.HWT101_UnlockRegister) for HWT101_UnlockRegister
    hwt101_driver.o(i.HWT101_ResetYaw) refers to hwt101_driver.o(i.HWT101_SendCommand) for HWT101_SendCommand
    hwt101_driver.o(i.HWT101_ResetYaw) refers to hwt101_driver.o(i.HWT101_SaveConfig) for HWT101_SaveConfig
    hwt101_driver.o(i.HWT101_ResetYaw) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_SaveConfig) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_SaveConfig) refers to usart.o(i.my_printf) for my_printf
    hwt101_driver.o(i.HWT101_SaveConfig) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_SendCommand) refers to usart.o(i.my_printf) for my_printf
    hwt101_driver.o(i.HWT101_SendCommand) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_SetBaudRate) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_SetBaudRate) refers to hwt101_driver.o(i.HWT101_UnlockRegister) for HWT101_UnlockRegister
    hwt101_driver.o(i.HWT101_SetBaudRate) refers to hwt101_driver.o(i.HWT101_SendCommand) for HWT101_SendCommand
    hwt101_driver.o(i.HWT101_SetBaudRate) refers to hwt101_driver.o(i.HWT101_SaveConfig) for HWT101_SaveConfig
    hwt101_driver.o(i.HWT101_SetBaudRate) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_SetOutputRate) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_SetOutputRate) refers to hwt101_driver.o(i.HWT101_UnlockRegister) for HWT101_UnlockRegister
    hwt101_driver.o(i.HWT101_SetOutputRate) refers to hwt101_driver.o(i.HWT101_SendCommand) for HWT101_SendCommand
    hwt101_driver.o(i.HWT101_SetOutputRate) refers to hwt101_driver.o(i.HWT101_SaveConfig) for HWT101_SaveConfig
    hwt101_driver.o(i.HWT101_SetOutputRate) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_StartManualCalibration) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_StartManualCalibration) refers to hwt101_driver.o(i.HWT101_UnlockRegister) for HWT101_UnlockRegister
    hwt101_driver.o(i.HWT101_StartManualCalibration) refers to hwt101_driver.o(i.HWT101_SendCommand) for HWT101_SendCommand
    hwt101_driver.o(i.HWT101_StartManualCalibration) refers to hwt101_driver.o(i.HWT101_SaveConfig) for HWT101_SaveConfig
    hwt101_driver.o(i.HWT101_StartManualCalibration) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_StopManualCalibration) refers to hwt101_driver.o(i.HWT101_ValidateParams) for HWT101_ValidateParams
    hwt101_driver.o(i.HWT101_StopManualCalibration) refers to hwt101_driver.o(i.HWT101_UnlockRegister) for HWT101_UnlockRegister
    hwt101_driver.o(i.HWT101_StopManualCalibration) refers to hwt101_driver.o(i.HWT101_SendCommand) for HWT101_SendCommand
    hwt101_driver.o(i.HWT101_StopManualCalibration) refers to hwt101_driver.o(i.HWT101_SaveConfig) for HWT101_SaveConfig
    hwt101_driver.o(i.HWT101_StopManualCalibration) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hwt101_driver.o(i.HWT101_UnlockRegister) refers to usart.o(i.my_printf) for my_printf
    hwt101_driver.o(i.HWT101_UnlockRegister) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    encoder_driver.o(i.Encoder_Driver_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    led_driver.o(i.Led_Display) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    led_driver.o(i.Led_Display) refers to led_driver.o(.data) for .data
    ebtn.o(i.bit_array_cmp) refers to memcmp.o(.text) for memcmp
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_combo_btn_add_btn_by_idx) for ebtn_combo_btn_add_btn_by_idx
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_combo_btn_remove_btn_by_idx) for ebtn_combo_btn_remove_btn_by_idx
    ebtn.o(i.ebtn_combo_register) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_get_btn_by_key_id) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_get_btn_index_by_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_btn_dyn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_key_id) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_get_config) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_get_total_btn_cnt) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_init) refers to memseta.o(.text) for __aeabi_memclr4
    ebtn.o(i.ebtn_init) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(i.ebtn_is_btn_in_process) for ebtn_is_btn_in_process
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.bit_array_assign) for bit_array_assign
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.ebtn_process_with_curr_state) for ebtn_process_with_curr_state
    ebtn.o(i.ebtn_process) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_and) for bit_array_and
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_cmp) for bit_array_cmp
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to memseta.o(.text) for __aeabi_memclr4
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_and) for bit_array_and
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_cmp) for bit_array_cmp
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_or) for bit_array_or
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn) for ebtn_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn_combo) for ebtn_process_btn_combo
    ebtn.o(i.ebtn_process_with_curr_state) refers to memcpya.o(.text) for __aeabi_memcpy4
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_register) refers to ebtn.o(i.ebtn_get_total_btn_cnt) for ebtn_get_total_btn_cnt
    ebtn.o(i.ebtn_register) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_set_config) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.prv_process_btn) refers to ebtn.o(.bss) for .bss
    ebtn_driver.o(i.Ebtn_Init) refers to ebtn.o(i.ebtn_init) for ebtn_init
    ebtn_driver.o(i.Ebtn_Init) refers to ebtn.o(i.ebtn_set_config) for ebtn_set_config
    ebtn_driver.o(i.Ebtn_Init) refers to key_app.o(i.my_handle_key_event) for my_handle_key_event
    ebtn_driver.o(i.Ebtn_Init) refers to ebtn_driver.o(i.my_get_key_state) for my_get_key_state
    ebtn_driver.o(i.Ebtn_Init) refers to ebtn_driver.o(.data) for .data
    ebtn_driver.o(i.my_get_key_state) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    ebtn_driver.o(.data) refers to ebtn_driver.o(.constdata) for defaul_ebtn_param
    hardware_iic.o(i.IIC_Anolog_Normalize) refers to hardware_iic.o(i.IIC_WriteBytes) for IIC_WriteBytes
    hardware_iic.o(i.IIC_Get_Anolog) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_Get_Digtal) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_Get_Offset) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_Get_Single_Anolog) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_ReadByte) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    hardware_iic.o(i.IIC_ReadByte) refers to i2c.o(.bss) for hi2c2
    hardware_iic.o(i.IIC_ReadBytes) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(i.IIC_ReadBytes) refers to i2c.o(.bss) for hi2c2
    hardware_iic.o(i.IIC_WriteByte) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    hardware_iic.o(i.IIC_WriteByte) refers to i2c.o(.bss) for hi2c2
    hardware_iic.o(i.IIC_WriteBytes) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    hardware_iic.o(i.IIC_WriteBytes) refers to i2c.o(.bss) for hi2c2
    hardware_iic.o(i.Ping) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    pid.o(i.pid_calculate_incremental) refers to pid.o(i.pid_out_limit) for pid_out_limit
    pid.o(i.pid_calculate_positional) refers to pid.o(i.pid_out_limit) for pid_out_limit
    pid.o(i.pid_set_target) refers to pid.o(i.pid_init) for pid_init
    ringbuffer.o(i.rt_ringbuffer_data_len) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_get) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_get) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_peek) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(i.rt_ringbuffer_put) for rt_ringbuffer_put
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr4
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to uart_driver.o(.bss) for .bss
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for huart1
    uart_driver.o(i.Uart_Printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart_driver.o(i.Uart_Printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WR_DATA) for OLED_WR_DATA
    oled.o(i.OLED_DisplayMode) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_DrawBMP) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_DrawBMP) refers to oled.o(i.OLED_WR_DATA) for OLED_WR_DATA
    oled.o(i.OLED_HorizontalShift) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_Init) refers to oled.o(.data) for .data
    oled.o(i.OLED_IntensityControl) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_On) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_On) refers to oled.o(i.OLED_WR_DATA) for OLED_WR_DATA
    oled.o(i.OLED_Set_Pos) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_ShowCHinese) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_ShowCHinese) refers to oled.o(i.OLED_WR_DATA) for OLED_WR_DATA
    oled.o(i.OLED_ShowCHinese) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_WR_DATA) for OLED_WR_DATA
    oled.o(i.OLED_ShowChar) refers to oled.o(.data) for .data
    oled.o(i.OLED_ShowNum) refers to oled.o(i.oled_pow) for oled_pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Showdecimal) refers to oled.o(i.oled_pow) for oled_pow
    oled.o(i.OLED_Showdecimal) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Some_HorizontalShift) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_VerticalAndHorizontalShift) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_WR_CMD) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_WR_CMD) refers to i2c.o(.bss) for hi2c2
    oled.o(i.OLED_WR_DATA) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_WR_DATA) refers to i2c.o(.bss) for hi2c2
    emm_v5.o(i.Emm_V5_En_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Interrupt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to memseta.o(.text) for __aeabi_memclr4
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Set_O) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Trigger_Return) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Parse_Response) refers to memseta.o(.text) for __aeabi_memclr4
    emm_v5.o(i.Emm_V5_Pos_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Read_Sys_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Reset_Clog_Pro) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Stop_Now) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Synchronous_motion) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Vel_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    scheduler.o(i.Scheduler_Init) refers to scheduler.o(i.System_Init) for System_Init
    scheduler.o(i.Scheduler_Init) refers to scheduler.o(.data) for .data
    scheduler.o(i.Scheduler_Run) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.Scheduler_Run) refers to scheduler.o(.data) for .data
    scheduler.o(i.System_Init) refers to motor_app.o(i.Motor_Init) for Motor_Init
    scheduler.o(i.System_Init) refers to encoder_app.o(i.Encoder_Init) for Encoder_Init
    scheduler.o(i.System_Init) refers to oled.o(i.OLED_Init) for OLED_Init
    scheduler.o(i.System_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    scheduler.o(i.System_Init) refers to led_app.o(i.Led_Init) for Led_Init
    scheduler.o(i.System_Init) refers to key_app.o(i.Key_Init) for Key_Init
    scheduler.o(i.System_Init) refers to uart_app.o(i.Uart_Init) for Uart_Init
    scheduler.o(i.System_Init) refers to pid_app.o(i.PID_Init) for PID_Init
    scheduler.o(i.System_Init) refers to stepmotor_app.o(i.StepMotor_Init) for StepMotor_Init
    scheduler.o(.data) refers to oled_app.o(i.oled_display_debug_data) for oled_display_debug_data
    scheduler.o(.data) refers to led_app.o(i.Led_Task) for Led_Task
    scheduler.o(.data) refers to encoder_app.o(i.Encoder_Task) for Encoder_Task
    scheduler.o(.data) refers to key_app.o(i.Key_Task) for Key_Task
    scheduler.o(.data) refers to uart_app.o(i.Uart_Task) for Uart_Task
    motor_app.o(i.Motor_Init) refers to motor_driver.o(i.Motor_Create) for Motor_Create
    motor_app.o(i.Motor_Init) refers to tim.o(.bss) for htim1
    motor_app.o(i.Motor_Init) refers to motor_app.o(.bss) for .bss
    encoder_app.o(i.Encoder_Init) refers to encoder_driver.o(i.Encoder_Driver_Init) for Encoder_Driver_Init
    encoder_app.o(i.Encoder_Init) refers to tim.o(.bss) for htim4
    encoder_app.o(i.Encoder_Init) refers to encoder_app.o(.bss) for .bss
    encoder_app.o(i.Encoder_Task) refers to encoder_driver.o(i.Encoder_Driver_Update) for Encoder_Driver_Update
    encoder_app.o(i.Encoder_Task) refers to encoder_app.o(.bss) for .bss
    led_app.o(i.Led_Init) refers to led_driver.o(i.Led_Display) for Led_Display
    led_app.o(i.Led_Init) refers to led_app.o(.data) for .data
    led_app.o(i.Led_Task) refers to led_driver.o(i.Led_Display) for Led_Display
    led_app.o(i.Led_Task) refers to led_app.o(.data) for .data
    key_app.o(i.Key_Init) refers to ebtn_driver.o(i.Ebtn_Init) for Ebtn_Init
    key_app.o(i.Key_Task) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    key_app.o(i.Key_Task) refers to ebtn.o(i.ebtn_process) for ebtn_process
    key_app.o(i.my_handle_key_event) refers to stepmotor_app.o(i.StepMotor_Move_Pulses_Speed) for StepMotor_Move_Pulses_Speed
    key_app.o(i.my_handle_key_event) refers to led_app.o(.data) for led_buf
    gray_app.o(i.Gray_Task) refers to hardware_iic.o(i.IIC_Get_Digtal) for IIC_Get_Digtal
    gray_app.o(i.Gray_Task) refers to hardware_iic.o(i.IIC_Anolog_Normalize) for IIC_Anolog_Normalize
    gray_app.o(i.Gray_Task) refers to gray_app.o(.data) for .data
    pid_app.o(i.PID_Init) refers to pid.o(i.pid_init) for pid_init
    pid_app.o(i.PID_Init) refers to pid.o(i.pid_set_target) for pid_set_target
    pid_app.o(i.PID_Init) refers to pid_app.o(.data) for .data
    pid_app.o(i.PID_Init) refers to pid_app.o(.bss) for .bss
    uart_app.o(i.Uart_Init) refers to ringbuffer.o(i.rt_ringbuffer_init) for rt_ringbuffer_init
    uart_app.o(i.Uart_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart_app.o(i.Uart_Init) refers to uart_driver.o(.bss) for ring_buffer_input
    uart_app.o(i.Uart_Init) refers to uart_driver.o(.bss) for ring_buffer
    uart_app.o(i.Uart_Init) refers to usart.o(.bss) for huart1
    uart_app.o(i.Uart_Init) refers to uart_driver.o(.bss) for ring_buffer_input2
    uart_app.o(i.Uart_Init) refers to uart_driver.o(.bss) for ring_buffer_input4
    uart_app.o(i.Uart_Init) refers to uart_driver.o(.bss) for ring_buffer_input5
    uart_app.o(i.Uart_Init) refers to uart_driver.o(.bss) for ring_buffer_input6
    uart_app.o(i.Uart_Task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    uart_app.o(i.Uart_Task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    uart_app.o(i.Uart_Task) refers to usart.o(i.my_printf) for my_printf
    uart_app.o(i.Uart_Task) refers to memseta.o(.text) for __aeabi_memclr
    uart_app.o(i.Uart_Task) refers to hwt101_driver.o(i.HWT101_ProcessBuffer) for HWT101_ProcessBuffer
    uart_app.o(i.Uart_Task) refers to hwt101_driver.o(i.HWT101_GetYaw) for HWT101_GetYaw
    uart_app.o(i.Uart_Task) refers to hwt101_driver.o(i.HWT101_GetGyroZ) for HWT101_GetGyroZ
    uart_app.o(i.Uart_Task) refers to hwt101_driver.o(i.HWT101_GetData) for HWT101_GetData
    uart_app.o(i.Uart_Task) refers to f2d.o(.text) for __aeabi_f2d
    uart_app.o(i.Uart_Task) refers to uart_driver.o(.bss) for ring_buffer
    uart_app.o(i.Uart_Task) refers to uart_driver.o(.bss) for uart_data_buffer
    uart_app.o(i.Uart_Task) refers to usart.o(.bss) for huart1
    uart_app.o(i.Uart_Task) refers to uart_driver.o(.bss) for uart2_data_buffer
    uart_app.o(i.Uart_Task) refers to uart_driver.o(.bss) for uart4_data_buffer
    uart_app.o(i.Uart_Task) refers to uart_driver.o(.bss) for uart5_data_buffer
    uart_app.o(i.Uart_Task) refers to main.o(.bss) for hwt101
    uart_app.o(i.Uart_Task) refers to uart_driver.o(.bss) for uart6_data_buffer
    oled_app.o(i.oled_app_init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled_app.o(i.oled_app_init) refers to oled.o(i.OLED_Init) for OLED_Init
    oled_app.o(i.oled_app_init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled_app.o(i.oled_app_init) refers to oled.o(i.OLED_Display_On) for OLED_Display_On
    oled_app.o(i.oled_app_init) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled_app.o(i.oled_app_init) refers to oled_app.o(.data) for .data
    oled_app.o(i.oled_app_task) refers to oled_app.o(i.oled_display_debug_data) for oled_display_debug_data
    oled_app.o(i.oled_app_task) refers to oled_app.o(.data) for .data
    oled_app.o(i.oled_display_debug_data) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    stepmotor_app.o(i.Motor_X_Receive_Data) refers to usart.o(i.my_printf) for my_printf
    stepmotor_app.o(i.Motor_X_Receive_Data) refers to stepmotor_app.o(.data) for .data
    stepmotor_app.o(i.Motor_X_Receive_Data) refers to stepmotor_app.o(.bss) for .bss
    stepmotor_app.o(i.Motor_X_Receive_Data) refers to usart.o(.bss) for huart1
    stepmotor_app.o(i.Motor_Y_Receive_Data) refers to usart.o(i.my_printf) for my_printf
    stepmotor_app.o(i.Motor_Y_Receive_Data) refers to stepmotor_app.o(.data) for .data
    stepmotor_app.o(i.Motor_Y_Receive_Data) refers to stepmotor_app.o(.bss) for .bss
    stepmotor_app.o(i.Motor_Y_Receive_Data) refers to usart.o(.bss) for huart1
    stepmotor_app.o(i.StepMotor_Check_Ready) refers to usart.o(i.my_printf) for my_printf
    stepmotor_app.o(i.StepMotor_Check_Ready) refers to stepmotor_app.o(.data) for .data
    stepmotor_app.o(i.StepMotor_Check_Ready) refers to usart.o(.bss) for huart1
    stepmotor_app.o(i.StepMotor_Clear_Ready_Flags) refers to usart.o(i.my_printf) for my_printf
    stepmotor_app.o(i.StepMotor_Clear_Ready_Flags) refers to stepmotor_app.o(.data) for .data
    stepmotor_app.o(i.StepMotor_Clear_Ready_Flags) refers to usart.o(.bss) for huart1
    stepmotor_app.o(i.StepMotor_Get_All_Pulse_Info) refers to stepmotor_app.o(.data) for .data
    stepmotor_app.o(i.StepMotor_Get_Motor_Status) refers to stepmotor_app.o(.data) for .data
    stepmotor_app.o(i.StepMotor_Get_Pulse_Count) refers to stepmotor_app.o(.data) for .data
    stepmotor_app.o(i.StepMotor_Get_Target_Pulses) refers to stepmotor_app.o(.data) for .data
    stepmotor_app.o(i.StepMotor_Init) refers to emm_v5.o(i.Emm_V5_En_Control) for Emm_V5_En_Control
    stepmotor_app.o(i.StepMotor_Init) refers to stepmotor_app.o(i.StepMotor_Stop) for StepMotor_Stop
    stepmotor_app.o(i.StepMotor_Init) refers to stepmotor_app.o(i.StepMotor_Clear_Ready_Flags) for StepMotor_Clear_Ready_Flags
    stepmotor_app.o(i.StepMotor_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    stepmotor_app.o(i.StepMotor_Init) refers to usart.o(i.my_printf) for my_printf
    stepmotor_app.o(i.StepMotor_Init) refers to usart.o(.bss) for huart2
    stepmotor_app.o(i.StepMotor_Init) refers to stepmotor_app.o(.data) for .data
    stepmotor_app.o(i.StepMotor_Init_Encoder) refers to stepmotor_app.o(i.StepMotor_Clear_Ready_Flags) for StepMotor_Clear_Ready_Flags
    stepmotor_app.o(i.StepMotor_Init_Encoder) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    stepmotor_app.o(i.StepMotor_Init_Encoder) refers to usart.o(i.my_printf) for my_printf
    stepmotor_app.o(i.StepMotor_Init_Encoder) refers to stepmotor_app.o(.data) for .data
    stepmotor_app.o(i.StepMotor_Init_Encoder) refers to usart.o(.bss) for huart2
    stepmotor_app.o(i.StepMotor_Move_Pulses) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    stepmotor_app.o(i.StepMotor_Move_Pulses) refers to stepmotor_app.o(.data) for .data
    stepmotor_app.o(i.StepMotor_Move_Pulses) refers to usart.o(.bss) for huart4
    stepmotor_app.o(i.StepMotor_Move_Pulses_Async) refers to stepmotor_app.o(i.StepMotor_Clear_Ready_Flags) for StepMotor_Clear_Ready_Flags
    stepmotor_app.o(i.StepMotor_Move_Pulses_Async) refers to stepmotor_app.o(i.StepMotor_Move_Pulses) for StepMotor_Move_Pulses
    stepmotor_app.o(i.StepMotor_Move_Pulses_Async) refers to usart.o(i.my_printf) for my_printf
    stepmotor_app.o(i.StepMotor_Move_Pulses_Async) refers to usart.o(.bss) for huart1
    stepmotor_app.o(i.StepMotor_Move_Pulses_Speed) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    stepmotor_app.o(i.StepMotor_Move_Pulses_Speed) refers to stepmotor_app.o(.data) for .data
    stepmotor_app.o(i.StepMotor_Move_Pulses_Speed) refers to usart.o(.bss) for huart4
    stepmotor_app.o(i.StepMotor_Move_Pulses_Wait) refers to stepmotor_app.o(i.StepMotor_Clear_Ready_Flags) for StepMotor_Clear_Ready_Flags
    stepmotor_app.o(i.StepMotor_Move_Pulses_Wait) refers to stepmotor_app.o(i.StepMotor_Move_Pulses) for StepMotor_Move_Pulses
    stepmotor_app.o(i.StepMotor_Move_Pulses_Wait) refers to usart.o(i.my_printf) for my_printf
    stepmotor_app.o(i.StepMotor_Move_Pulses_Wait) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stepmotor_app.o(i.StepMotor_Move_Pulses_Wait) refers to stepmotor_app.o(i.StepMotor_Check_Ready) for StepMotor_Check_Ready
    stepmotor_app.o(i.StepMotor_Move_Pulses_Wait) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    stepmotor_app.o(i.StepMotor_Move_Pulses_Wait) refers to usart.o(.bss) for huart1
    stepmotor_app.o(i.StepMotor_Print_Pulse_Status) refers to usart.o(i.my_printf) for my_printf
    stepmotor_app.o(i.StepMotor_Print_Pulse_Status) refers to usart.o(.bss) for huart1
    stepmotor_app.o(i.StepMotor_Print_Pulse_Status) refers to stepmotor_app.o(.data) for .data
    stepmotor_app.o(i.StepMotor_Reset_Pulse_Count) refers to usart.o(i.my_printf) for my_printf
    stepmotor_app.o(i.StepMotor_Reset_Pulse_Count) refers to stepmotor_app.o(.data) for .data
    stepmotor_app.o(i.StepMotor_Reset_Pulse_Count) refers to usart.o(.bss) for huart1
    stepmotor_app.o(i.StepMotor_Set_Speed) refers to emm_v5.o(i.Emm_V5_Vel_Control) for Emm_V5_Vel_Control
    stepmotor_app.o(i.StepMotor_Set_Speed) refers to usart.o(.bss) for huart2
    stepmotor_app.o(i.StepMotor_Stop) refers to emm_v5.o(i.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    stepmotor_app.o(i.StepMotor_Stop) refers to usart.o(.bss) for huart2
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f407xx.o(HEAP), (1536 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.STMFLASH_GetFlashSector), (160 bytes).
    Removing main.o(i.SaveCoordinatesToFlash), (60 bytes).
    Removing main.o(i.WriteFlashData), (148 bytes).
    Removing main.o(.data), (8 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (56 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing spi.o(i.HAL_SPI_MspDeInit), (40 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (28 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (72 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (232 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (68 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (560 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (58 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (98 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (496 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (196 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (552 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (320 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (452 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (212 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (300 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (184 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (516 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (220 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (404 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (208 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (372 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAAbort), (188 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAError), (64 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt), (274 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Flush_DR), (16 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_ITError), (344 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (218 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (244 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite), (156 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (182 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR), (280 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_SB), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead), (252 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_AF), (144 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (112 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (96 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (316 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (308 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (104 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (80 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (200 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (92 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (80 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (152 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (64 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (204 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2432 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (98 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (288 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (82 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (144 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (168 bytes).
    Removing stm32f4xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Abort), (292 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT), (292 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAPause), (38 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAResume), (38 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAStop), (66 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit), (46 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_GetError), (4 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_GetState), (6 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler), (252 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_MspInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive), (340 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA), (232 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT), (172 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit), (358 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive), (496 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA), (272 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT), (156 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA), (204 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT), (144 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT), (48 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT), (48 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT), (48 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT), (48 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR), (88 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR), (28 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR), (144 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR), (76 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR), (124 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError), (16 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAError), (34 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt), (106 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback), (102 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt), (100 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt), (90 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback), (116 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction), (92 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction), (108 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT), (32 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT), (32 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT), (32 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT), (32 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout), (188 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (228 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (460 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler), (304 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (224 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (28 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (120 bytes).
    Removing motor_driver.o(.rev16_text), (4 bytes).
    Removing motor_driver.o(.revsh_text), (4 bytes).
    Removing motor_driver.o(.rrx_text), (6 bytes).
    Removing motor_driver.o(i.Motor_Enable), (80 bytes).
    Removing motor_driver.o(i.Motor_GetState), (18 bytes).
    Removing motor_driver.o(i.Motor_SetSpeed), (184 bytes).
    Removing motor_driver.o(i.Motor_Stop), (74 bytes).
    Removing motor_driver.o(i.Motor_ValidateParams), (20 bytes).
    Removing motor_driver.o(i.Speed_To_PWM), (44 bytes).
    Removing hwt101_driver.o(.rev16_text), (4 bytes).
    Removing hwt101_driver.o(.revsh_text), (4 bytes).
    Removing hwt101_driver.o(.rrx_text), (6 bytes).
    Removing hwt101_driver.o(i.HWT101_Create), (84 bytes).
    Removing hwt101_driver.o(i.HWT101_Enable), (64 bytes).
    Removing hwt101_driver.o(i.HWT101_GetState), (20 bytes).
    Removing hwt101_driver.o(i.HWT101_ResetYaw), (64 bytes).
    Removing hwt101_driver.o(i.HWT101_SaveConfig), (52 bytes).
    Removing hwt101_driver.o(i.HWT101_SendCommand), (48 bytes).
    Removing hwt101_driver.o(i.HWT101_SetBaudRate), (70 bytes).
    Removing hwt101_driver.o(i.HWT101_SetOutputRate), (74 bytes).
    Removing hwt101_driver.o(i.HWT101_StartManualCalibration), (64 bytes).
    Removing hwt101_driver.o(i.HWT101_StopManualCalibration), (64 bytes).
    Removing hwt101_driver.o(i.HWT101_UnlockRegister), (36 bytes).
    Removing encoder_driver.o(.rev16_text), (4 bytes).
    Removing encoder_driver.o(.revsh_text), (4 bytes).
    Removing encoder_driver.o(.rrx_text), (6 bytes).
    Removing led_driver.o(.rev16_text), (4 bytes).
    Removing led_driver.o(.revsh_text), (4 bytes).
    Removing led_driver.o(.rrx_text), (6 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_add_btn), (26 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_add_btn_by_idx), (22 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_remove_btn), (26 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_remove_btn_by_idx), (22 bytes).
    Removing ebtn.o(i.ebtn_combo_register), (44 bytes).
    Removing ebtn.o(i.ebtn_get_btn_by_key_id), (72 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_btn), (6 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_btn_dyn), (6 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_key_id), (64 bytes).
    Removing ebtn.o(i.ebtn_get_config), (12 bytes).
    Removing ebtn.o(i.ebtn_get_total_btn_cnt), (24 bytes).
    Removing ebtn.o(i.ebtn_is_btn_active), (16 bytes).
    Removing ebtn.o(i.ebtn_is_btn_in_process), (16 bytes).
    Removing ebtn.o(i.ebtn_is_in_process), (116 bytes).
    Removing ebtn.o(i.ebtn_register), (56 bytes).
    Removing ebtn_driver.o(.rev16_text), (4 bytes).
    Removing ebtn_driver.o(.revsh_text), (4 bytes).
    Removing ebtn_driver.o(.rrx_text), (6 bytes).
    Removing hardware_iic.o(.rev16_text), (4 bytes).
    Removing hardware_iic.o(.revsh_text), (4 bytes).
    Removing hardware_iic.o(.rrx_text), (6 bytes).
    Removing hardware_iic.o(i.IIC_Anolog_Normalize), (16 bytes).
    Removing hardware_iic.o(i.IIC_Get_Anolog), (22 bytes).
    Removing hardware_iic.o(i.IIC_Get_Digtal), (20 bytes).
    Removing hardware_iic.o(i.IIC_Get_Offset), (24 bytes).
    Removing hardware_iic.o(i.IIC_Get_Single_Anolog), (22 bytes).
    Removing hardware_iic.o(i.IIC_ReadByte), (32 bytes).
    Removing hardware_iic.o(i.IIC_ReadBytes), (36 bytes).
    Removing hardware_iic.o(i.IIC_WriteByte), (44 bytes).
    Removing hardware_iic.o(i.IIC_WriteBytes), (36 bytes).
    Removing hardware_iic.o(i.Ping), (30 bytes).
    Removing pid.o(i.pid_app_limit_integral), (36 bytes).
    Removing pid.o(i.pid_calculate_incremental), (122 bytes).
    Removing pid.o(i.pid_calculate_positional), (102 bytes).
    Removing pid.o(i.pid_constrain), (32 bytes).
    Removing pid.o(i.pid_out_limit), (38 bytes).
    Removing pid.o(i.pid_reset), (40 bytes).
    Removing pid.o(i.pid_set_limit), (6 bytes).
    Removing pid.o(i.pid_set_params), (6 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_getchar), (70 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_peek), (70 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_put_force), (164 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar), (74 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar_force), (92 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_reset), (8 bytes).
    Removing uart_driver.o(.rev16_text), (4 bytes).
    Removing uart_driver.o(.revsh_text), (4 bytes).
    Removing uart_driver.o(.rrx_text), (6 bytes).
    Removing uart_driver.o(i.Uart_Printf), (50 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_DisplayMode), (4 bytes).
    Removing oled.o(i.OLED_Display_Off), (24 bytes).
    Removing oled.o(i.OLED_Display_On), (24 bytes).
    Removing oled.o(i.OLED_DrawBMP), (72 bytes).
    Removing oled.o(i.OLED_HorizontalShift), (62 bytes).
    Removing oled.o(i.OLED_IntensityControl), (20 bytes).
    Removing oled.o(i.OLED_On), (52 bytes).
    Removing oled.o(i.OLED_ShowCHinese), (92 bytes).
    Removing oled.o(i.OLED_ShowNum), (116 bytes).
    Removing oled.o(i.OLED_Showdecimal), (310 bytes).
    Removing oled.o(i.OLED_Some_HorizontalShift), (66 bytes).
    Removing oled.o(i.OLED_VerticalAndHorizontalShift), (68 bytes).
    Removing oled.o(i.oled_pow), (16 bytes).
    Removing oled.o(.constdata), (192 bytes).
    Removing emm_v5.o(.rev16_text), (4 bytes).
    Removing emm_v5.o(.revsh_text), (4 bytes).
    Removing emm_v5.o(.rrx_text), (6 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode), (56 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Interrupt), (48 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Modify_Params), (158 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Set_O), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Trigger_Return), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Parse_Response), (482 bytes).
    Removing emm_v5.o(i.Emm_V5_Read_Sys_Params), (142 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_Clog_Pro), (48 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero), (48 bytes).
    Removing emm_v5.o(i.Emm_V5_Synchronous_motion), (48 bytes).
    Removing emm_v5.o(i.Emm_V5_Vel_Control), (68 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing motor_app.o(.rev16_text), (4 bytes).
    Removing motor_app.o(.revsh_text), (4 bytes).
    Removing motor_app.o(.rrx_text), (6 bytes).
    Removing encoder_app.o(.rev16_text), (4 bytes).
    Removing encoder_app.o(.revsh_text), (4 bytes).
    Removing encoder_app.o(.rrx_text), (6 bytes).
    Removing led_app.o(.rev16_text), (4 bytes).
    Removing led_app.o(.revsh_text), (4 bytes).
    Removing led_app.o(.rrx_text), (6 bytes).
    Removing key_app.o(.rev16_text), (4 bytes).
    Removing key_app.o(.revsh_text), (4 bytes).
    Removing key_app.o(.rrx_text), (6 bytes).
    Removing gray_app.o(.rev16_text), (4 bytes).
    Removing gray_app.o(.revsh_text), (4 bytes).
    Removing gray_app.o(.rrx_text), (6 bytes).
    Removing gray_app.o(i.Gray_Init), (2 bytes).
    Removing gray_app.o(i.Gray_Task), (28 bytes).
    Removing gray_app.o(.data), (1 bytes).
    Removing gray_app.o(.data), (4 bytes).
    Removing pid_app.o(.rev16_text), (4 bytes).
    Removing pid_app.o(.revsh_text), (4 bytes).
    Removing pid_app.o(.rrx_text), (6 bytes).
    Removing pid_app.o(.data), (4 bytes).
    Removing pid_app.o(.data), (1 bytes).
    Removing pid_app.o(.data), (4 bytes).
    Removing pid_app.o(.data), (4 bytes).
    Removing uart_app.o(.rev16_text), (4 bytes).
    Removing uart_app.o(.revsh_text), (4 bytes).
    Removing uart_app.o(.rrx_text), (6 bytes).
    Removing oled_app.o(.rev16_text), (4 bytes).
    Removing oled_app.o(.revsh_text), (4 bytes).
    Removing oled_app.o(.rrx_text), (6 bytes).
    Removing oled_app.o(i.oled_app_init), (128 bytes).
    Removing oled_app.o(i.oled_app_task), (20 bytes).
    Removing oled_app.o(.data), (1 bytes).
    Removing stepmotor_app.o(.rev16_text), (4 bytes).
    Removing stepmotor_app.o(.revsh_text), (4 bytes).
    Removing stepmotor_app.o(.rrx_text), (6 bytes).
    Removing stepmotor_app.o(i.StepMotor_Check_Ready), (144 bytes).
    Removing stepmotor_app.o(i.StepMotor_Get_All_Pulse_Info), (36 bytes).
    Removing stepmotor_app.o(i.StepMotor_Get_Motor_Status), (28 bytes).
    Removing stepmotor_app.o(i.StepMotor_Get_Pulse_Count), (28 bytes).
    Removing stepmotor_app.o(i.StepMotor_Get_Target_Pulses), (28 bytes).
    Removing stepmotor_app.o(i.StepMotor_Init_Encoder), (84 bytes).
    Removing stepmotor_app.o(i.StepMotor_Move_Pulses), (116 bytes).
    Removing stepmotor_app.o(i.StepMotor_Move_Pulses_Async), (68 bytes).
    Removing stepmotor_app.o(i.StepMotor_Move_Pulses_Wait), (248 bytes).
    Removing stepmotor_app.o(i.StepMotor_Print_Pulse_Status), (120 bytes).
    Removing stepmotor_app.o(i.StepMotor_Reset_Pulse_Count), (68 bytes).
    Removing stepmotor_app.o(i.StepMotor_Set_Speed), (124 bytes).

686 unused section(s) (total 53701 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\APP\StepMotor_app.c                   0x00000000   Number         0  stepmotor_app.o ABSOLUTE
    ..\APP\encoder_app.c                     0x00000000   Number         0  encoder_app.o ABSOLUTE
    ..\APP\gray_app.c                        0x00000000   Number         0  gray_app.o ABSOLUTE
    ..\APP\key_app.c                         0x00000000   Number         0  key_app.o ABSOLUTE
    ..\APP\led_app.c                         0x00000000   Number         0  led_app.o ABSOLUTE
    ..\APP\motor_app.c                       0x00000000   Number         0  motor_app.o ABSOLUTE
    ..\APP\oled_app.c                        0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\APP\pid_app.c                         0x00000000   Number         0  pid_app.o ABSOLUTE
    ..\APP\scheduler.c                       0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\APP\uart_app.c                        0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\Components\Ebtn\ebtn.c                0x00000000   Number         0  ebtn.o ABSOLUTE
    ..\Components\Ebtn\ebtn_driver.c         0x00000000   Number         0  ebtn_driver.o ABSOLUTE
    ..\Components\Encoder\encoder_driver.c   0x00000000   Number         0  encoder_driver.o ABSOLUTE
    ..\Components\Grayscale\hardware_iic.c   0x00000000   Number         0  hardware_iic.o ABSOLUTE
    ..\Components\Hwt101\hwt101_driver.c     0x00000000   Number         0  hwt101_driver.o ABSOLUTE
    ..\Components\LED\led_driver.c           0x00000000   Number         0  led_driver.o ABSOLUTE
    ..\Components\Motor\motor_driver.c       0x00000000   Number         0  motor_driver.o ABSOLUTE
    ..\Components\Oled\oled.c                0x00000000   Number         0  oled.o ABSOLUTE
    ..\Components\Oled\oled_font.c           0x00000000   Number         0  oled_font.o ABSOLUTE
    ..\Components\PID\pid.c                  0x00000000   Number         0  pid.o ABSOLUTE
    ..\Components\StepMotor\Emm_V5.c         0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\Components\Uart\ringbuffer.c          0x00000000   Number         0  ringbuffer.o ABSOLUTE
    ..\Components\Uart\uart_driver.c         0x00000000   Number         0  uart_driver.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\\APP\\StepMotor_app.c                 0x00000000   Number         0  stepmotor_app.o ABSOLUTE
    ..\\APP\\encoder_app.c                   0x00000000   Number         0  encoder_app.o ABSOLUTE
    ..\\APP\\gray_app.c                      0x00000000   Number         0  gray_app.o ABSOLUTE
    ..\\APP\\key_app.c                       0x00000000   Number         0  key_app.o ABSOLUTE
    ..\\APP\\led_app.c                       0x00000000   Number         0  led_app.o ABSOLUTE
    ..\\APP\\motor_app.c                     0x00000000   Number         0  motor_app.o ABSOLUTE
    ..\\APP\\oled_app.c                      0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\\APP\\pid_app.c                       0x00000000   Number         0  pid_app.o ABSOLUTE
    ..\\APP\\scheduler.c                     0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\\APP\\uart_app.c                      0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\\Components\\Ebtn\\ebtn_driver.c      0x00000000   Number         0  ebtn_driver.o ABSOLUTE
    ..\\Components\\Encoder\\encoder_driver.c 0x00000000   Number         0  encoder_driver.o ABSOLUTE
    ..\\Components\\Grayscale\\hardware_iic.c 0x00000000   Number         0  hardware_iic.o ABSOLUTE
    ..\\Components\\Hwt101\\hwt101_driver.c  0x00000000   Number         0  hwt101_driver.o ABSOLUTE
    ..\\Components\\LED\\led_driver.c        0x00000000   Number         0  led_driver.o ABSOLUTE
    ..\\Components\\Motor\\motor_driver.c    0x00000000   Number         0  motor_driver.o ABSOLUTE
    ..\\Components\\Oled\\oled.c             0x00000000   Number         0  oled.o ABSOLUTE
    ..\\Components\\StepMotor\\Emm_V5.c      0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\\Components\\Uart\\uart_driver.c      0x00000000   Number         0  uart_driver.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000198   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000198   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000198   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000198   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x0800019c   Section       36  startup_stm32f407xx.o(.text)
    $v0                                      0x0800019c   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x080001c0   Section        0  uldiv.o(.text)
    .text                                    0x08000222   Section        0  memcpya.o(.text)
    .text                                    0x08000246   Section        0  memseta.o(.text)
    .text                                    0x0800026a   Section        0  memcmp.o(.text)
    .text                                    0x08000284   Section        0  f2d.o(.text)
    .text                                    0x080002aa   Section        0  uidiv.o(.text)
    .text                                    0x080002d6   Section        0  llshl.o(.text)
    .text                                    0x080002f4   Section        0  llushr.o(.text)
    .text                                    0x08000314   Section        0  iusefp.o(.text)
    .text                                    0x08000314   Section        0  dadd.o(.text)
    .text                                    0x08000462   Section        0  dmul.o(.text)
    .text                                    0x08000546   Section        0  ddiv.o(.text)
    .text                                    0x08000624   Section        0  dfixul.o(.text)
    .text                                    0x08000654   Section       48  cdrcmple.o(.text)
    .text                                    0x08000684   Section       36  init.o(.text)
    .text                                    0x080006a8   Section        0  llsshr.o(.text)
    .text                                    0x080006cc   Section        0  depilogue.o(.text)
    .text                                    0x08000786   Section        0  __dczerorl2.o(.text)
    i.BusFault_Handler                       0x080007dc   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA1_Stream0_IRQHandler                0x080007e0   Section        0  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    i.DMA1_Stream2_IRQHandler                0x080007ec   Section        0  stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler)
    i.DMA1_Stream5_IRQHandler                0x080007f8   Section        0  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    i.DMA2_Stream1_IRQHandler                0x08000804   Section        0  stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler)
    i.DMA2_Stream2_IRQHandler                0x08000810   Section        0  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x0800081c   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x0800081d   Thumb Code    34  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x08000844   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x08000845   Thumb Code    84  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x08000898   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08000899   Thumb Code    40  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x080008c0   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Ebtn_Init                              0x080008c4   Section        0  ebtn_driver.o(i.Ebtn_Init)
    i.Emm_V5_En_Control                      0x080008f8   Section        0  emm_v5.o(i.Emm_V5_En_Control)
    i.Emm_V5_Pos_Control                     0x08000930   Section        0  emm_v5.o(i.Emm_V5_Pos_Control)
    i.Emm_V5_Stop_Now                        0x080009a0   Section        0  emm_v5.o(i.Emm_V5_Stop_Now)
    i.Encoder_Driver_Init                    0x080009d4   Section        0  encoder_driver.o(i.Encoder_Driver_Init)
    i.Encoder_Driver_Update                  0x08000a00   Section        0  encoder_driver.o(i.Encoder_Driver_Update)
    i.Encoder_Init                           0x08000a54   Section        0  encoder_app.o(i.Encoder_Init)
    i.Encoder_Task                           0x08000a7c   Section        0  encoder_app.o(i.Encoder_Task)
    i.Error_Handler                          0x08000a94   Section        0  main.o(i.Error_Handler)
    i.HAL_DMA_Abort                          0x08000a98   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08000b2a   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08000b50   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08000cf0   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08000dc4   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08000e34   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08000e58   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08001048   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x08001052   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x0800105c   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_Init                           0x08001068   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_Mem_Write                      0x080011f0   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    i.HAL_I2C_MspInit                        0x08001320   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x0800138c   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x0800139c   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x080013d0   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08001410   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08001440   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x0800145c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x0800149c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x080014c0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x080015f4   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08001614   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08001634   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08001694   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SPI_Init                           0x08001a00   Section        0  stm32f4xx_hal_spi.o(i.HAL_SPI_Init)
    i.HAL_SPI_MspInit                        0x08001abc   Section        0  spi.o(i.HAL_SPI_MspInit)
    i.HAL_SYSTICK_Config                     0x08001b24   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x08001b4c   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08001ba0   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08001c30   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08001c8c   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_ConfigClockSource              0x08001cb4   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_Encoder_Init                   0x08001d90   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x08001e34   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_Encoder_Start                  0x08001ed8   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    i.HAL_TIM_MspPostInit                    0x08001f68   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_PWM_ConfigChannel              0x08001fbc   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x08002088   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x080020e2   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_Start                      0x080020e4   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x080021ac   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x080021f8   Section        0  uart_driver.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_DMAStop                       0x08002338   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_ErrorCallback                 0x080023a8   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x080023ac   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x0800262c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08002690   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x0800298c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x080029a8   Section        0  usart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x080029f8   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x080029fa   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08002a9a   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HWT101_ConvertGyroData                 0x08002a9c   Section        0  hwt101_driver.o(i.HWT101_ConvertGyroData)
    HWT101_ConvertGyroData                   0x08002a9d   Thumb Code    32  hwt101_driver.o(i.HWT101_ConvertGyroData)
    i.HWT101_GetData                         0x08002ac4   Section        0  hwt101_driver.o(i.HWT101_GetData)
    i.HWT101_GetGyroZ                        0x08002ae8   Section        0  hwt101_driver.o(i.HWT101_GetGyroZ)
    i.HWT101_GetYaw                          0x08002b10   Section        0  hwt101_driver.o(i.HWT101_GetYaw)
    i.HWT101_ProcessBuffer                   0x08002b38   Section        0  hwt101_driver.o(i.HWT101_ProcessBuffer)
    i.HWT101_ValidateParams                  0x08002c80   Section        0  hwt101_driver.o(i.HWT101_ValidateParams)
    HWT101_ValidateParams                    0x08002c81   Thumb Code    16  hwt101_driver.o(i.HWT101_ValidateParams)
    i.HardFault_Handler                      0x08002c90   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.I2C_IsAcknowledgeFailed                0x08002c92   Section        0  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    I2C_IsAcknowledgeFailed                  0x08002c93   Thumb Code    46  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    i.I2C_RequestMemoryWrite                 0x08002cc0   Section        0  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    I2C_RequestMemoryWrite                   0x08002cc1   Thumb Code   162  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    i.I2C_WaitOnBTFFlagUntilTimeout          0x08002d68   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    I2C_WaitOnBTFFlagUntilTimeout            0x08002d69   Thumb Code    86  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    i.I2C_WaitOnFlagUntilTimeout             0x08002dc0   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x08002dc1   Thumb Code   144  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnMasterAddressFlagUntilTimeout 0x08002e50   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x08002e51   Thumb Code   188  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    i.I2C_WaitOnTXEFlagUntilTimeout          0x08002f0c   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    I2C_WaitOnTXEFlagUntilTimeout            0x08002f0d   Thumb Code    86  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    i.Key_Init                               0x08002f62   Section        0  key_app.o(i.Key_Init)
    i.Key_Task                               0x08002f66   Section        0  key_app.o(i.Key_Task)
    i.Led_Display                            0x08002f74   Section        0  led_driver.o(i.Led_Display)
    i.Led_Init                               0x08003004   Section        0  led_app.o(i.Led_Init)
    i.Led_Task                               0x08003010   Section        0  led_app.o(i.Led_Task)
    i.MX_DMA_Init                            0x0800301c   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x08003098   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C2_Init                           0x080031b8   Section        0  i2c.o(i.MX_I2C2_Init)
    i.MX_SPI1_Init                           0x080031f8   Section        0  spi.o(i.MX_SPI1_Init)
    i.MX_TIM1_Init                           0x08003240   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM3_Init                           0x08003318   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM4_Init                           0x08003384   Section        0  tim.o(i.MX_TIM4_Init)
    i.MX_UART4_Init                          0x080033f0   Section        0  usart.o(i.MX_UART4_Init)
    i.MX_UART5_Init                          0x08003428   Section        0  usart.o(i.MX_UART5_Init)
    i.MX_USART1_UART_Init                    0x08003460   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08003498   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART6_UART_Init                    0x080034d0   Section        0  usart.o(i.MX_USART6_UART_Init)
    i.MemManage_Handler                      0x08003508   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.Motor_Create                           0x0800350a   Section        0  motor_driver.o(i.Motor_Create)
    i.Motor_Init                             0x0800357c   Section        0  motor_app.o(i.Motor_Init)
    i.Motor_X_Receive_Data                   0x080035bc   Section        0  stepmotor_app.o(i.Motor_X_Receive_Data)
    i.Motor_Y_Receive_Data                   0x08003680   Section        0  stepmotor_app.o(i.Motor_Y_Receive_Data)
    i.NMI_Handler                            0x08003744   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.OLED_Clear                             0x08003746   Section        0  oled.o(i.OLED_Clear)
    i.OLED_Init                              0x0800377c   Section        0  oled.o(i.OLED_Init)
    i.OLED_Set_Pos                           0x0800379c   Section        0  oled.o(i.OLED_Set_Pos)
    i.OLED_ShowChar                          0x080037c0   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowString                        0x0800385c   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_WR_CMD                            0x080038b4   Section        0  oled.o(i.OLED_WR_CMD)
    i.OLED_WR_DATA                           0x080038d8   Section        0  oled.o(i.OLED_WR_DATA)
    i.PID_Init                               0x080038fc   Section        0  pid_app.o(i.PID_Init)
    i.PendSV_Handler                         0x0800395c   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.ReadCoordinatesFromFlash               0x08003960   Section        0  main.o(i.ReadCoordinatesFromFlash)
    i.SVC_Handler                            0x08003984   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.Scheduler_Init                         0x08003988   Section        0  scheduler.o(i.Scheduler_Init)
    i.Scheduler_Run                          0x0800399c   Section        0  scheduler.o(i.Scheduler_Run)
    i.StepMotor_Clear_Ready_Flags            0x080039d8   Section        0  stepmotor_app.o(i.StepMotor_Clear_Ready_Flags)
    i.StepMotor_Init                         0x08003a2c   Section        0  stepmotor_app.o(i.StepMotor_Init)
    i.StepMotor_Move_Pulses_Speed            0x08003a9c   Section        0  stepmotor_app.o(i.StepMotor_Move_Pulses_Speed)
    i.StepMotor_Stop                         0x08003b10   Section        0  stepmotor_app.o(i.StepMotor_Stop)
    i.SysTick_Handler                        0x08003b34   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08003b38   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08003bcc   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.System_Init                            0x08003bdc   Section        0  scheduler.o(i.System_Init)
    i.TIM_Base_SetConfig                     0x08003c08   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x08003cd8   Section        0  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_ETR_SetConfig                      0x08003cf2   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08003d06   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08003d07   Thumb Code    16  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x08003d18   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x08003d19   Thumb Code    88  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08003d78   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08003de4   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08003de5   Thumb Code    96  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08003e4c   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08003e4d   Thumb Code    70  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08003e9c   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08003e9d   Thumb Code    34  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x08003ebe   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08003ebf   Thumb Code    36  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART4_IRQHandler                       0x08003ee4   Section        0  stm32f4xx_it.o(i.UART4_IRQHandler)
    i.UART5_IRQHandler                       0x08003ef0   Section        0  stm32f4xx_it.o(i.UART5_IRQHandler)
    i.UART_DMAAbortOnError                   0x08003efc   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08003efd   Thumb Code    14  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x08003f0a   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x08003f0b   Thumb Code    74  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x08003f54   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x08003f55   Thumb Code   134  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x08003fda   Section        0  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x08003fdb   Thumb Code    30  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x08003ff8   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08003ff9   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTxTransfer                     0x08004046   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08004047   Thumb Code    28  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x08004062   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08004063   Thumb Code   194  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08004124   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08004125   Thumb Code   258  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x08004230   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_Start_Receive_IT                  0x080042d0   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08004306   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08004307   Thumb Code   114  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08004378   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08004384   Section        0  stm32f4xx_it.o(i.USART2_IRQHandler)
    i.USART6_IRQHandler                      0x08004390   Section        0  stm32f4xx_it.o(i.USART6_IRQHandler)
    i.Uart_Init                              0x0800439c   Section        0  uart_app.o(i.Uart_Init)
    i.Uart_Task                              0x080044a4   Section        0  uart_app.o(i.Uart_Task)
    i.UsageFault_Handler                     0x08004664   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__0vsnprintf                           0x08004668   Section        0  printfa.o(i.__0vsnprintf)
    i.__NVIC_SetPriority                     0x08004694   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08004695   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__scatterload_copy                     0x080046b4   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080046c2   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080046c4   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x080046d4   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x080046d5   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x08004858   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08004859   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08004f34   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08004f35   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08004f58   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08004f59   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x08004f86   Section        0  printfa.o(i._snputc)
    _snputc                                  0x08004f87   Thumb Code    22  printfa.o(i._snputc)
    i.bit_array_and                          0x08004f9c   Section        0  ebtn.o(i.bit_array_and)
    bit_array_and                            0x08004f9d   Thumb Code    38  ebtn.o(i.bit_array_and)
    i.bit_array_assign                       0x08004fc2   Section        0  ebtn.o(i.bit_array_assign)
    bit_array_assign                         0x08004fc3   Thumb Code    30  ebtn.o(i.bit_array_assign)
    i.bit_array_cmp                          0x08004fe0   Section        0  ebtn.o(i.bit_array_cmp)
    bit_array_cmp                            0x08004fe1   Thumb Code    12  ebtn.o(i.bit_array_cmp)
    i.bit_array_get                          0x08004fec   Section        0  ebtn.o(i.bit_array_get)
    bit_array_get                            0x08004fed   Thumb Code    18  ebtn.o(i.bit_array_get)
    i.bit_array_or                           0x08005000   Section        0  ebtn.o(i.bit_array_or)
    bit_array_or                             0x08005001   Thumb Code    38  ebtn.o(i.bit_array_or)
    i.ebtn_init                              0x08005028   Section        0  ebtn.o(i.ebtn_init)
    i.ebtn_process                           0x0800506c   Section        0  ebtn.o(i.ebtn_process)
    i.ebtn_process_btn                       0x080050d0   Section        0  ebtn.o(i.ebtn_process_btn)
    ebtn_process_btn                         0x080050d1   Thumb Code    46  ebtn.o(i.ebtn_process_btn)
    i.ebtn_process_btn_combo                 0x08005100   Section        0  ebtn.o(i.ebtn_process_btn_combo)
    ebtn_process_btn_combo                   0x08005101   Thumb Code   172  ebtn.o(i.ebtn_process_btn_combo)
    i.ebtn_process_with_curr_state           0x080051ac   Section        0  ebtn.o(i.ebtn_process_with_curr_state)
    i.ebtn_set_config                        0x08005304   Section        0  ebtn.o(i.ebtn_set_config)
    i.main                                   0x08005310   Section        0  main.o(i.main)
    i.my_get_key_state                       0x08005390   Section        0  ebtn_driver.o(i.my_get_key_state)
    i.my_handle_key_event                    0x080053c4   Section        0  key_app.o(i.my_handle_key_event)
    i.my_printf                              0x080053ec   Section        0  usart.o(i.my_printf)
    i.oled_display_debug_data                0x08005420   Section        0  oled_app.o(i.oled_display_debug_data)
    i.pid_init                               0x08005440   Section        0  pid.o(i.pid_init)
    i.pid_set_target                         0x08005470   Section        0  pid.o(i.pid_set_target)
    i.prv_process_btn                        0x08005484   Section        0  ebtn.o(i.prv_process_btn)
    prv_process_btn                          0x08005485   Thumb Code   328  ebtn.o(i.prv_process_btn)
    i.rt_ringbuffer_data_len                 0x080055d0   Section        0  ringbuffer.o(i.rt_ringbuffer_data_len)
    i.rt_ringbuffer_get                      0x08005600   Section        0  ringbuffer.o(i.rt_ringbuffer_get)
    i.rt_ringbuffer_init                     0x0800566e   Section        0  ringbuffer.o(i.rt_ringbuffer_init)
    i.rt_ringbuffer_put                      0x08005694   Section        0  ringbuffer.o(i.rt_ringbuffer_put)
    i.rt_ringbuffer_status                   0x08005706   Section        0  ringbuffer.o(i.rt_ringbuffer_status)
    .constdata                               0x08005726   Section        8  stm32f4xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x08005726   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x0800572e   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x0800573e   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x08005746   Section       14  ebtn_driver.o(.constdata)
    defaul_ebtn_param                        0x08005746   Data          14  ebtn_driver.o(.constdata)
    .data                                    0x20000000   Section        8  main.o(.data)
    .data                                    0x20000008   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x20000014   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000018   Section        1  led_driver.o(.data)
    led_temp_old                             0x20000018   Data           1  led_driver.o(.data)
    .data                                    0x2000001c   Section       28  ebtn_driver.o(.data)
    static_buttons                           0x2000001c   Data          28  ebtn_driver.o(.data)
    .data                                    0x20000038   Section     2079  oled.o(.data)
    F6x8                                     0x20000038   Data         552  oled.o(.data)
    F8X16                                    0x20000260   Data        1504  oled.o(.data)
    .data                                    0x20000858   Section       64  scheduler.o(.data)
    scheduler_task                           0x2000085c   Data          60  scheduler.o(.data)
    .data                                    0x20000898   Section        4  led_app.o(.data)
    .data                                    0x2000089c   Section       40  pid_app.o(.data)
    .data                                    0x200008c4   Section       28  stepmotor_app.o(.data)
    .bss                                     0x200008e0   Section       68  main.o(.bss)
    .bss                                     0x20000924   Section       84  i2c.o(.bss)
    .bss                                     0x20000978   Section       88  spi.o(.bss)
    .bss                                     0x200009d0   Section      216  tim.o(.bss)
    .bss                                     0x20000aa8   Section      840  usart.o(.bss)
    .bss                                     0x20000df0   Section       52  ebtn.o(.bss)
    ebtn_default                             0x20000df0   Data          52  ebtn.o(.bss)
    .bss                                     0x20000e24   Section      700  uart_driver.o(.bss)
    .bss                                     0x200010e0   Section      128  uart_driver.o(.bss)
    .bss                                     0x20001160   Section      128  uart_driver.o(.bss)
    .bss                                     0x200011e0   Section      128  uart_driver.o(.bss)
    .bss                                     0x20001260   Section      128  uart_driver.o(.bss)
    .bss                                     0x200012e0   Section      128  uart_driver.o(.bss)
    .bss                                     0x20001360   Section      128  uart_driver.o(.bss)
    .bss                                     0x200013e0   Section      128  uart_driver.o(.bss)
    .bss                                     0x20001460   Section      128  uart_driver.o(.bss)
    .bss                                     0x200014e0   Section      128  uart_driver.o(.bss)
    .bss                                     0x20001560   Section      128  uart_driver.o(.bss)
    .bss                                     0x200015e0   Section       40  motor_app.o(.bss)
    .bss                                     0x20001608   Section       32  encoder_app.o(.bss)
    .bss                                     0x20001628   Section      120  pid_app.o(.bss)
    .bss                                     0x200016a0   Section       20  stepmotor_app.o(.bss)
    STACK                                    0x200016b8   Section     4096  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000199   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000199   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x0800019d   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x080001c1   Thumb Code    98  uldiv.o(.text)
    __aeabi_memcpy                           0x08000223   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000223   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000223   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x08000247   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000247   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000247   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000255   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000255   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000255   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000259   Thumb Code    18  memseta.o(.text)
    memcmp                                   0x0800026b   Thumb Code    26  memcmp.o(.text)
    __aeabi_f2d                              0x08000285   Thumb Code    38  f2d.o(.text)
    __aeabi_uidiv                            0x080002ab   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x080002ab   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x080002d7   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080002d7   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080002f5   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080002f5   Thumb Code     0  llushr.o(.text)
    __I$use$fp                               0x08000315   Thumb Code     0  iusefp.o(.text)
    __aeabi_dadd                             0x08000315   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000457   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x0800045d   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x08000463   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000547   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x08000625   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08000655   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08000685   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000685   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x080006a9   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080006a9   Thumb Code     0  llsshr.o(.text)
    _double_round                            0x080006cd   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080006eb   Thumb Code   156  depilogue.o(.text)
    __decompress                             0x08000787   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x08000787   Thumb Code    86  __dczerorl2.o(.text)
    BusFault_Handler                         0x080007dd   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    DMA1_Stream0_IRQHandler                  0x080007e1   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    DMA1_Stream2_IRQHandler                  0x080007ed   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler)
    DMA1_Stream5_IRQHandler                  0x080007f9   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    DMA2_Stream1_IRQHandler                  0x08000805   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x08000811   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    DebugMon_Handler                         0x080008c1   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Ebtn_Init                                0x080008c5   Thumb Code    40  ebtn_driver.o(i.Ebtn_Init)
    Emm_V5_En_Control                        0x080008f9   Thumb Code    56  emm_v5.o(i.Emm_V5_En_Control)
    Emm_V5_Pos_Control                       0x08000931   Thumb Code   112  emm_v5.o(i.Emm_V5_Pos_Control)
    Emm_V5_Stop_Now                          0x080009a1   Thumb Code    52  emm_v5.o(i.Emm_V5_Stop_Now)
    Encoder_Driver_Init                      0x080009d5   Thumb Code    38  encoder_driver.o(i.Encoder_Driver_Init)
    Encoder_Driver_Update                    0x08000a01   Thumb Code    72  encoder_driver.o(i.Encoder_Driver_Update)
    Encoder_Init                             0x08000a55   Thumb Code    28  encoder_app.o(i.Encoder_Init)
    Encoder_Task                             0x08000a7d   Thumb Code    20  encoder_app.o(i.Encoder_Task)
    Error_Handler                            0x08000a95   Thumb Code     4  main.o(i.Error_Handler)
    HAL_DMA_Abort                            0x08000a99   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08000b2b   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08000b51   Thumb Code   412  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08000cf1   Thumb Code   206  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08000dc5   Thumb Code   110  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08000e35   Thumb Code    32  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08000e59   Thumb Code   450  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08001049   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x08001053   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x0800105d   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_I2C_Init                             0x08001069   Thumb Code   376  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_Mem_Write                        0x080011f1   Thumb Code   294  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    HAL_I2C_MspInit                          0x08001321   Thumb Code    94  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x0800138d   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x0800139d   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x080013d1   Thumb Code    54  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08001411   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08001441   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x0800145d   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x0800149d   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x080014c1   Thumb Code   288  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x080015f5   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08001615   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08001635   Thumb Code    88  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001695   Thumb Code   856  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SPI_Init                             0x08001a01   Thumb Code   188  stm32f4xx_hal_spi.o(i.HAL_SPI_Init)
    HAL_SPI_MspInit                          0x08001abd   Thumb Code    92  spi.o(i.HAL_SPI_MspInit)
    HAL_SYSTICK_Config                       0x08001b25   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_ConfigBreakDeadTime            0x08001b4d   Thumb Code    84  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x08001ba1   Thumb Code   116  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08001c31   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08001c8d   Thumb Code    30  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_ConfigClockSource                0x08001cb5   Thumb Code   220  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x08001d91   Thumb Code   164  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08001e35   Thumb Code   142  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_Encoder_Start                    0x08001ed9   Thumb Code   142  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    HAL_TIM_MspPostInit                      0x08001f69   Thumb Code    72  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_PWM_ConfigChannel                0x08001fbd   Thumb Code   204  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08002089   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x080020e3   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_Start                        0x080020e5   Thumb Code   172  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_UARTEx_ReceiveToIdle_DMA             0x080021ad   Thumb Code    74  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x080021f9   Thumb Code   248  uart_driver.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x08002339   Thumb Code   112  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x080023a9   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x080023ad   Thumb Code   636  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x0800262d   Thumb Code   100  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08002691   Thumb Code   712  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x0800298d   Thumb Code    28  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x080029a9   Thumb Code    58  usart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x080029f9   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x080029fb   Thumb Code   160  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08002a9b   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HWT101_GetData                           0x08002ac5   Thumb Code    34  hwt101_driver.o(i.HWT101_GetData)
    HWT101_GetGyroZ                          0x08002ae9   Thumb Code    36  hwt101_driver.o(i.HWT101_GetGyroZ)
    HWT101_GetYaw                            0x08002b11   Thumb Code    36  hwt101_driver.o(i.HWT101_GetYaw)
    HWT101_ProcessBuffer                     0x08002b39   Thumb Code   320  hwt101_driver.o(i.HWT101_ProcessBuffer)
    HardFault_Handler                        0x08002c91   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    Key_Init                                 0x08002f63   Thumb Code     4  key_app.o(i.Key_Init)
    Key_Task                                 0x08002f67   Thumb Code    14  key_app.o(i.Key_Task)
    Led_Display                              0x08002f75   Thumb Code   132  led_driver.o(i.Led_Display)
    Led_Init                                 0x08003005   Thumb Code     8  led_app.o(i.Led_Init)
    Led_Task                                 0x08003011   Thumb Code     8  led_app.o(i.Led_Task)
    MX_DMA_Init                              0x0800301d   Thumb Code   120  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x08003099   Thumb Code   266  gpio.o(i.MX_GPIO_Init)
    MX_I2C2_Init                             0x080031b9   Thumb Code    50  i2c.o(i.MX_I2C2_Init)
    MX_SPI1_Init                             0x080031f9   Thumb Code    62  spi.o(i.MX_SPI1_Init)
    MX_TIM1_Init                             0x08003241   Thumb Code   206  tim.o(i.MX_TIM1_Init)
    MX_TIM3_Init                             0x08003319   Thumb Code    98  tim.o(i.MX_TIM3_Init)
    MX_TIM4_Init                             0x08003385   Thumb Code    98  tim.o(i.MX_TIM4_Init)
    MX_UART4_Init                            0x080033f1   Thumb Code    48  usart.o(i.MX_UART4_Init)
    MX_UART5_Init                            0x08003429   Thumb Code    48  usart.o(i.MX_UART5_Init)
    MX_USART1_UART_Init                      0x08003461   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08003499   Thumb Code    48  usart.o(i.MX_USART2_UART_Init)
    MX_USART6_UART_Init                      0x080034d1   Thumb Code    48  usart.o(i.MX_USART6_UART_Init)
    MemManage_Handler                        0x08003509   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    Motor_Create                             0x0800350b   Thumb Code   114  motor_driver.o(i.Motor_Create)
    Motor_Init                               0x0800357d   Thumb Code    50  motor_app.o(i.Motor_Init)
    Motor_X_Receive_Data                     0x080035bd   Thumb Code   152  stepmotor_app.o(i.Motor_X_Receive_Data)
    Motor_Y_Receive_Data                     0x08003681   Thumb Code   152  stepmotor_app.o(i.Motor_Y_Receive_Data)
    NMI_Handler                              0x08003745   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x08003747   Thumb Code    52  oled.o(i.OLED_Clear)
    OLED_Init                                0x0800377d   Thumb Code    28  oled.o(i.OLED_Init)
    OLED_Set_Pos                             0x0800379d   Thumb Code    34  oled.o(i.OLED_Set_Pos)
    OLED_ShowChar                            0x080037c1   Thumb Code   148  oled.o(i.OLED_ShowChar)
    OLED_ShowString                          0x0800385d   Thumb Code    88  oled.o(i.OLED_ShowString)
    OLED_WR_CMD                              0x080038b5   Thumb Code    32  oled.o(i.OLED_WR_CMD)
    OLED_WR_DATA                             0x080038d9   Thumb Code    32  oled.o(i.OLED_WR_DATA)
    PID_Init                                 0x080038fd   Thumb Code    84  pid_app.o(i.PID_Init)
    PendSV_Handler                           0x0800395d   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    ReadCoordinatesFromFlash                 0x08003961   Thumb Code    32  main.o(i.ReadCoordinatesFromFlash)
    SVC_Handler                              0x08003985   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    Scheduler_Init                           0x08003989   Thumb Code    14  scheduler.o(i.Scheduler_Init)
    Scheduler_Run                            0x0800399d   Thumb Code    56  scheduler.o(i.Scheduler_Run)
    StepMotor_Clear_Ready_Flags              0x080039d9   Thumb Code    18  stepmotor_app.o(i.StepMotor_Clear_Ready_Flags)
    StepMotor_Init                           0x08003a2d   Thumb Code    68  stepmotor_app.o(i.StepMotor_Init)
    StepMotor_Move_Pulses_Speed              0x08003a9d   Thumb Code   104  stepmotor_app.o(i.StepMotor_Move_Pulses_Speed)
    StepMotor_Stop                           0x08003b11   Thumb Code    26  stepmotor_app.o(i.StepMotor_Stop)
    SysTick_Handler                          0x08003b35   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08003b39   Thumb Code   138  main.o(i.SystemClock_Config)
    SystemInit                               0x08003bcd   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    System_Init                              0x08003bdd   Thumb Code    42  scheduler.o(i.System_Init)
    TIM_Base_SetConfig                       0x08003c09   Thumb Code   164  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x08003cd9   Thumb Code    26  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_ETR_SetConfig                        0x08003cf3   Thumb Code    20  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x08003d79   Thumb Code    98  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART4_IRQHandler                         0x08003ee5   Thumb Code     6  stm32f4xx_it.o(i.UART4_IRQHandler)
    UART5_IRQHandler                         0x08003ef1   Thumb Code     6  stm32f4xx_it.o(i.UART5_IRQHandler)
    UART_Start_Receive_DMA                   0x08004231   Thumb Code   146  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    UART_Start_Receive_IT                    0x080042d1   Thumb Code    54  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x08004379   Thumb Code     6  stm32f4xx_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08004385   Thumb Code     6  stm32f4xx_it.o(i.USART2_IRQHandler)
    USART6_IRQHandler                        0x08004391   Thumb Code     6  stm32f4xx_it.o(i.USART6_IRQHandler)
    Uart_Init                                0x0800439d   Thumb Code   164  uart_app.o(i.Uart_Init)
    Uart_Task                                0x080044a5   Thumb Code   296  uart_app.o(i.Uart_Task)
    UsageFault_Handler                       0x08004665   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __0vsnprintf                             0x08004669   Thumb Code    40  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x08004669   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x08004669   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x08004669   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x08004669   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __scatterload_copy                       0x080046b5   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080046c3   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080046c5   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    ebtn_init                                0x08005029   Thumb Code    64  ebtn.o(i.ebtn_init)
    ebtn_process                             0x0800506d   Thumb Code    94  ebtn.o(i.ebtn_process)
    ebtn_process_with_curr_state             0x080051ad   Thumb Code   340  ebtn.o(i.ebtn_process_with_curr_state)
    ebtn_set_config                          0x08005305   Thumb Code     8  ebtn.o(i.ebtn_set_config)
    main                                     0x08005311   Thumb Code    90  main.o(i.main)
    my_get_key_state                         0x08005391   Thumb Code    46  ebtn_driver.o(i.my_get_key_state)
    my_handle_key_event                      0x080053c5   Thumb Code    34  key_app.o(i.my_handle_key_event)
    my_printf                                0x080053ed   Thumb Code    50  usart.o(i.my_printf)
    oled_display_debug_data                  0x08005421   Thumb Code    18  oled_app.o(i.oled_display_debug_data)
    pid_init                                 0x08005441   Thumb Code    42  pid.o(i.pid_init)
    pid_set_target                           0x08005471   Thumb Code    20  pid.o(i.pid_set_target)
    rt_ringbuffer_data_len                   0x080055d1   Thumb Code    48  ringbuffer.o(i.rt_ringbuffer_data_len)
    rt_ringbuffer_get                        0x08005601   Thumb Code   110  ringbuffer.o(i.rt_ringbuffer_get)
    rt_ringbuffer_init                       0x0800566f   Thumb Code    38  ringbuffer.o(i.rt_ringbuffer_init)
    rt_ringbuffer_put                        0x08005695   Thumb Code   114  ringbuffer.o(i.rt_ringbuffer_put)
    rt_ringbuffer_status                     0x08005707   Thumb Code    32  ringbuffer.o(i.rt_ringbuffer_status)
    AHBPrescTable                            0x0800572e   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x0800573e   Data           8  system_stm32f4xx.o(.constdata)
    Region$$Table$$Base                      0x08005754   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08005774   Number         0  anon$$obj.o(Region$$Table)
    readCoordinates                          0x20000000   Data           8  main.o(.data)
    uwTickFreq                               0x20000008   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x2000000c   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x20000010   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x20000014   Data           4  system_stm32f4xx.o(.data)
    CMD_Data                                 0x20000840   Data          23  oled.o(.data)
    task_num                                 0x20000858   Data           1  scheduler.o(.data)
    led_buf                                  0x20000898   Data           4  led_app.o(.data)
    pid_params_X                             0x2000089c   Data          20  pid_app.o(.data)
    pid_params_Y                             0x200008b0   Data          20  pid_app.o(.data)
    motor_x_rx_state                         0x200008c4   Data           1  stepmotor_app.o(.data)
    motor_y_rx_state                         0x200008c5   Data           1  stepmotor_app.o(.data)
    motor_x_rx_counter                       0x200008c6   Data           1  stepmotor_app.o(.data)
    motor_y_rx_counter                       0x200008c7   Data           1  stepmotor_app.o(.data)
    motor_x_ready                            0x200008c8   Data           1  stepmotor_app.o(.data)
    motor_y_ready                            0x200008c9   Data           1  stepmotor_app.o(.data)
    stop_flag_car                            0x200008ca   Data           1  stepmotor_app.o(.data)
    motor_x_rx_byte                          0x200008cb   Data           1  stepmotor_app.o(.data)
    motor_y_rx_byte                          0x200008cc   Data           1  stepmotor_app.o(.data)
    motor_x_pulse_count                      0x200008d0   Data           4  stepmotor_app.o(.data)
    motor_y_pulse_count                      0x200008d4   Data           4  stepmotor_app.o(.data)
    motor_x_target_pulses                    0x200008d8   Data           4  stepmotor_app.o(.data)
    motor_y_target_pulses                    0x200008dc   Data           4  stepmotor_app.o(.data)
    hwt101                                   0x200008e0   Data          68  main.o(.bss)
    hi2c2                                    0x20000924   Data          84  i2c.o(.bss)
    hspi1                                    0x20000978   Data          88  spi.o(.bss)
    htim1                                    0x200009d0   Data          72  tim.o(.bss)
    htim3                                    0x20000a18   Data          72  tim.o(.bss)
    htim4                                    0x20000a60   Data          72  tim.o(.bss)
    huart4                                   0x20000aa8   Data          72  usart.o(.bss)
    huart5                                   0x20000af0   Data          72  usart.o(.bss)
    huart1                                   0x20000b38   Data          72  usart.o(.bss)
    huart2                                   0x20000b80   Data          72  usart.o(.bss)
    huart6                                   0x20000bc8   Data          72  usart.o(.bss)
    hdma_uart4_rx                            0x20000c10   Data          96  usart.o(.bss)
    hdma_uart5_rx                            0x20000c70   Data          96  usart.o(.bss)
    hdma_usart1_rx                           0x20000cd0   Data          96  usart.o(.bss)
    hdma_usart2_rx                           0x20000d30   Data          96  usart.o(.bss)
    hdma_usart6_rx                           0x20000d90   Data          96  usart.o(.bss)
    uart_rx_dma_buffer                       0x20000e24   Data         128  uart_driver.o(.bss)
    ring_buffer                              0x20000ea4   Data          12  uart_driver.o(.bss)
    uart2_rx_dma_buffer                      0x20000eb0   Data         128  uart_driver.o(.bss)
    ring_buffer2                             0x20000f30   Data          12  uart_driver.o(.bss)
    uart4_rx_dma_buffer                      0x20000f3c   Data         128  uart_driver.o(.bss)
    ring_buffer4                             0x20000fbc   Data          12  uart_driver.o(.bss)
    uart5_rx_dma_buffer                      0x20000fc8   Data         128  uart_driver.o(.bss)
    ring_buffer5                             0x20001048   Data          12  uart_driver.o(.bss)
    uart6_rx_dma_buffer                      0x20001054   Data         128  uart_driver.o(.bss)
    ring_buffer6                             0x200010d4   Data          12  uart_driver.o(.bss)
    ring_buffer_input                        0x200010e0   Data         128  uart_driver.o(.bss)
    uart_data_buffer                         0x20001160   Data         128  uart_driver.o(.bss)
    ring_buffer_input2                       0x200011e0   Data         128  uart_driver.o(.bss)
    uart2_data_buffer                        0x20001260   Data         128  uart_driver.o(.bss)
    ring_buffer_input4                       0x200012e0   Data         128  uart_driver.o(.bss)
    uart4_data_buffer                        0x20001360   Data         128  uart_driver.o(.bss)
    ring_buffer_input5                       0x200013e0   Data         128  uart_driver.o(.bss)
    uart5_data_buffer                        0x20001460   Data         128  uart_driver.o(.bss)
    ring_buffer_input6                       0x200014e0   Data         128  uart_driver.o(.bss)
    uart6_data_buffer                        0x20001560   Data         128  uart_driver.o(.bss)
    right_motor                              0x200015e0   Data          20  motor_app.o(.bss)
    left_motor                               0x200015f4   Data          20  motor_app.o(.bss)
    left_encoder                             0x20001608   Data          16  encoder_app.o(.bss)
    right_encoder                            0x20001618   Data          16  encoder_app.o(.bss)
    pid_speed_x                              0x20001628   Data          60  pid_app.o(.bss)
    pid_speed_y                              0x20001664   Data          60  pid_app.o(.bss)
    motor_x_rx_buffer                        0x200016a0   Data          10  stepmotor_app.o(.bss)
    motor_y_rx_buffer                        0x200016aa   Data          10  stepmotor_app.o(.bss)
    __initial_sp                             0x200026b8   Data           0  startup_stm32f407xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00006054, Max: 0x00080000, ABSOLUTE, COMPRESSED[0x00005cf4])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00005774, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000000   Code   RO         5531  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         5574    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         5577    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         5579    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         5581    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         5582    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000000   Code   RO         5584    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000198   0x08000198   0x00000000   Code   RO         5586    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         5575    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x0800019c   0x0800019c   0x00000024   Code   RO            4    .text               startup_stm32f407xx.o
    0x080001c0   0x080001c0   0x00000062   Code   RO         5534    .text               mc_w.l(uldiv.o)
    0x08000222   0x08000222   0x00000024   Code   RO         5536    .text               mc_w.l(memcpya.o)
    0x08000246   0x08000246   0x00000024   Code   RO         5538    .text               mc_w.l(memseta.o)
    0x0800026a   0x0800026a   0x0000001a   Code   RO         5540    .text               mc_w.l(memcmp.o)
    0x08000284   0x08000284   0x00000026   Code   RO         5572    .text               mf_w.l(f2d.o)
    0x080002aa   0x080002aa   0x0000002c   Code   RO         5591    .text               mc_w.l(uidiv.o)
    0x080002d6   0x080002d6   0x0000001e   Code   RO         5593    .text               mc_w.l(llshl.o)
    0x080002f4   0x080002f4   0x00000020   Code   RO         5595    .text               mc_w.l(llushr.o)
    0x08000314   0x08000314   0x00000000   Code   RO         5597    .text               mc_w.l(iusefp.o)
    0x08000314   0x08000314   0x0000014e   Code   RO         5598    .text               mf_w.l(dadd.o)
    0x08000462   0x08000462   0x000000e4   Code   RO         5600    .text               mf_w.l(dmul.o)
    0x08000546   0x08000546   0x000000de   Code   RO         5602    .text               mf_w.l(ddiv.o)
    0x08000624   0x08000624   0x00000030   Code   RO         5604    .text               mf_w.l(dfixul.o)
    0x08000654   0x08000654   0x00000030   Code   RO         5606    .text               mf_w.l(cdrcmple.o)
    0x08000684   0x08000684   0x00000024   Code   RO         5608    .text               mc_w.l(init.o)
    0x080006a8   0x080006a8   0x00000024   Code   RO         5611    .text               mc_w.l(llsshr.o)
    0x080006cc   0x080006cc   0x000000ba   Code   RO         5614    .text               mf_w.l(depilogue.o)
    0x08000786   0x08000786   0x00000056   Code   RO         5624    .text               mc_w.l(__dczerorl2.o)
    0x080007dc   0x080007dc   0x00000002   Code   RO          605    i.BusFault_Handler  stm32f4xx_it.o
    0x080007de   0x080007de   0x00000002   PAD
    0x080007e0   0x080007e0   0x0000000c   Code   RO          606    i.DMA1_Stream0_IRQHandler  stm32f4xx_it.o
    0x080007ec   0x080007ec   0x0000000c   Code   RO          607    i.DMA1_Stream2_IRQHandler  stm32f4xx_it.o
    0x080007f8   0x080007f8   0x0000000c   Code   RO          608    i.DMA1_Stream5_IRQHandler  stm32f4xx_it.o
    0x08000804   0x08000804   0x0000000c   Code   RO          609    i.DMA2_Stream1_IRQHandler  stm32f4xx_it.o
    0x08000810   0x08000810   0x0000000c   Code   RO          610    i.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x0800081c   0x0800081c   0x00000028   Code   RO         1679    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x08000844   0x08000844   0x00000054   Code   RO         1680    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x08000898   0x08000898   0x00000028   Code   RO         1681    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x080008c0   0x080008c0   0x00000002   Code   RO          611    i.DebugMon_Handler  stm32f4xx_it.o
    0x080008c2   0x080008c2   0x00000002   PAD
    0x080008c4   0x080008c4   0x00000034   Code   RO         4476    i.Ebtn_Init         ebtn_driver.o
    0x080008f8   0x080008f8   0x00000038   Code   RO         4944    i.Emm_V5_En_Control  emm_v5.o
    0x08000930   0x08000930   0x00000070   Code   RO         4951    i.Emm_V5_Pos_Control  emm_v5.o
    0x080009a0   0x080009a0   0x00000034   Code   RO         4955    i.Emm_V5_Stop_Now   emm_v5.o
    0x080009d4   0x080009d4   0x0000002c   Code   RO         4246    i.Encoder_Driver_Init  encoder_driver.o
    0x08000a00   0x08000a00   0x00000054   Code   RO         4247    i.Encoder_Driver_Update  encoder_driver.o
    0x08000a54   0x08000a54   0x00000028   Code   RO         5156    i.Encoder_Init      encoder_app.o
    0x08000a7c   0x08000a7c   0x00000018   Code   RO         5157    i.Encoder_Task      encoder_app.o
    0x08000a94   0x08000a94   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08000a98   0x08000a98   0x00000092   Code   RO         1682    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x08000b2a   0x08000b2a   0x00000024   Code   RO         1683    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08000b4e   0x08000b4e   0x00000002   PAD
    0x08000b50   0x08000b50   0x000001a0   Code   RO         1687    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08000cf0   0x08000cf0   0x000000d4   Code   RO         1688    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x08000dc4   0x08000dc4   0x0000006e   Code   RO         1692    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x08000e32   0x08000e32   0x00000002   PAD
    0x08000e34   0x08000e34   0x00000024   Code   RO         2119    i.HAL_Delay         stm32f4xx_hal.o
    0x08000e58   0x08000e58   0x000001f0   Code   RO         1575    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08001048   0x08001048   0x0000000a   Code   RO         1577    i.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x08001052   0x08001052   0x0000000a   Code   RO         1579    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x0800105c   0x0800105c   0x0000000c   Code   RO         2125    i.HAL_GetTick       stm32f4xx_hal.o
    0x08001068   0x08001068   0x00000188   Code   RO          776    i.HAL_I2C_Init      stm32f4xx_hal_i2c.o
    0x080011f0   0x080011f0   0x00000130   Code   RO          797    i.HAL_I2C_Mem_Write  stm32f4xx_hal_i2c.o
    0x08001320   0x08001320   0x0000006c   Code   RO          358    i.HAL_I2C_MspInit   i2c.o
    0x0800138c   0x0800138c   0x00000010   Code   RO         2131    i.HAL_IncTick       stm32f4xx_hal.o
    0x0800139c   0x0800139c   0x00000034   Code   RO         2132    i.HAL_Init          stm32f4xx_hal.o
    0x080013d0   0x080013d0   0x00000040   Code   RO         2133    i.HAL_InitTick      stm32f4xx_hal.o
    0x08001410   0x08001410   0x00000030   Code   RO          741    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08001440   0x08001440   0x0000001a   Code   RO         1967    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x0800145a   0x0800145a   0x00000002   PAD
    0x0800145c   0x0800145c   0x00000040   Code   RO         1973    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x0800149c   0x0800149c   0x00000024   Code   RO         1974    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x080014c0   0x080014c0   0x00000134   Code   RO         1221    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x080015f4   0x080015f4   0x00000020   Code   RO         1228    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08001614   0x08001614   0x00000020   Code   RO         1229    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08001634   0x08001634   0x00000060   Code   RO         1230    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08001694   0x08001694   0x0000036c   Code   RO         1233    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08001a00   0x08001a00   0x000000bc   Code   RO         2381    i.HAL_SPI_Init      stm32f4xx_hal_spi.o
    0x08001abc   0x08001abc   0x00000068   Code   RO          400    i.HAL_SPI_MspInit   spi.o
    0x08001b24   0x08001b24   0x00000028   Code   RO         1978    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08001b4c   0x08001b4c   0x00000054   Code   RO         3401    i.HAL_TIMEx_ConfigBreakDeadTime  stm32f4xx_hal_tim_ex.o
    0x08001ba0   0x08001ba0   0x00000090   Code   RO         3417    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08001c30   0x08001c30   0x0000005a   Code   RO         2694    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x08001c8a   0x08001c8a   0x00000002   PAD
    0x08001c8c   0x08001c8c   0x00000028   Code   RO          442    i.HAL_TIM_Base_MspInit  tim.o
    0x08001cb4   0x08001cb4   0x000000dc   Code   RO         2703    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x08001d90   0x08001d90   0x000000a4   Code   RO         2715    i.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x08001e34   0x08001e34   0x000000a4   Code   RO          444    i.HAL_TIM_Encoder_MspInit  tim.o
    0x08001ed8   0x08001ed8   0x0000008e   Code   RO         2718    i.HAL_TIM_Encoder_Start  stm32f4xx_hal_tim.o
    0x08001f66   0x08001f66   0x00000002   PAD
    0x08001f68   0x08001f68   0x00000054   Code   RO          445    i.HAL_TIM_MspPostInit  tim.o
    0x08001fbc   0x08001fbc   0x000000cc   Code   RO         2766    i.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x08002088   0x08002088   0x0000005a   Code   RO         2769    i.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x080020e2   0x080020e2   0x00000002   Code   RO         2771    i.HAL_TIM_PWM_MspInit  stm32f4xx_hal_tim.o
    0x080020e4   0x080020e4   0x000000c8   Code   RO         2774    i.HAL_TIM_PWM_Start  stm32f4xx_hal_tim.o
    0x080021ac   0x080021ac   0x0000004a   Code   RO         3675    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x080021f6   0x080021f6   0x00000002   PAD
    0x080021f8   0x080021f8   0x00000140   Code   RO         4746    i.HAL_UARTEx_RxEventCallback  uart_driver.o
    0x08002338   0x08002338   0x00000070   Code   RO         3689    i.HAL_UART_DMAStop  stm32f4xx_hal_uart.o
    0x080023a8   0x080023a8   0x00000002   Code   RO         3691    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x080023aa   0x080023aa   0x00000002   PAD
    0x080023ac   0x080023ac   0x00000280   Code   RO         3694    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x0800262c   0x0800262c   0x00000064   Code   RO         3695    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08002690   0x08002690   0x000002fc   Code   RO          514    i.HAL_UART_MspInit  usart.o
    0x0800298c   0x0800298c   0x0000001c   Code   RO         3700    i.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x080029a8   0x080029a8   0x00000050   Code   RO          515    i.HAL_UART_RxCpltCallback  usart.o
    0x080029f8   0x080029f8   0x00000002   Code   RO         3702    i.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x080029fa   0x080029fa   0x000000a0   Code   RO         3703    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08002a9a   0x08002a9a   0x00000002   Code   RO         3706    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08002a9c   0x08002a9c   0x00000028   Code   RO         4127    i.HWT101_ConvertGyroData  hwt101_driver.o
    0x08002ac4   0x08002ac4   0x00000022   Code   RO         4130    i.HWT101_GetData    hwt101_driver.o
    0x08002ae6   0x08002ae6   0x00000002   PAD
    0x08002ae8   0x08002ae8   0x00000028   Code   RO         4131    i.HWT101_GetGyroZ   hwt101_driver.o
    0x08002b10   0x08002b10   0x00000028   Code   RO         4133    i.HWT101_GetYaw     hwt101_driver.o
    0x08002b38   0x08002b38   0x00000148   Code   RO         4134    i.HWT101_ProcessBuffer  hwt101_driver.o
    0x08002c80   0x08002c80   0x00000010   Code   RO         4143    i.HWT101_ValidateParams  hwt101_driver.o
    0x08002c90   0x08002c90   0x00000002   Code   RO          612    i.HardFault_Handler  stm32f4xx_it.o
    0x08002c92   0x08002c92   0x0000002e   Code   RO          819    i.I2C_IsAcknowledgeFailed  stm32f4xx_hal_i2c.o
    0x08002cc0   0x08002cc0   0x000000a8   Code   RO          830    i.I2C_RequestMemoryWrite  stm32f4xx_hal_i2c.o
    0x08002d68   0x08002d68   0x00000056   Code   RO          834    i.I2C_WaitOnBTFFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08002dbe   0x08002dbe   0x00000002   PAD
    0x08002dc0   0x08002dc0   0x00000090   Code   RO          835    i.I2C_WaitOnFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08002e50   0x08002e50   0x000000bc   Code   RO          836    i.I2C_WaitOnMasterAddressFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08002f0c   0x08002f0c   0x00000056   Code   RO          838    i.I2C_WaitOnTXEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08002f62   0x08002f62   0x00000004   Code   RO         5229    i.Key_Init          key_app.o
    0x08002f66   0x08002f66   0x0000000e   Code   RO         5230    i.Key_Task          key_app.o
    0x08002f74   0x08002f74   0x00000090   Code   RO         4280    i.Led_Display       led_driver.o
    0x08003004   0x08003004   0x0000000c   Code   RO         5192    i.Led_Init          led_app.o
    0x08003010   0x08003010   0x0000000c   Code   RO         5193    i.Led_Task          led_app.o
    0x0800301c   0x0800301c   0x0000007c   Code   RO          333    i.MX_DMA_Init       dma.o
    0x08003098   0x08003098   0x00000120   Code   RO          309    i.MX_GPIO_Init      gpio.o
    0x080031b8   0x080031b8   0x00000040   Code   RO          359    i.MX_I2C2_Init      i2c.o
    0x080031f8   0x080031f8   0x00000048   Code   RO          401    i.MX_SPI1_Init      spi.o
    0x08003240   0x08003240   0x000000d8   Code   RO          446    i.MX_TIM1_Init      tim.o
    0x08003318   0x08003318   0x0000006c   Code   RO          447    i.MX_TIM3_Init      tim.o
    0x08003384   0x08003384   0x0000006c   Code   RO          448    i.MX_TIM4_Init      tim.o
    0x080033f0   0x080033f0   0x00000038   Code   RO          516    i.MX_UART4_Init     usart.o
    0x08003428   0x08003428   0x00000038   Code   RO          517    i.MX_UART5_Init     usart.o
    0x08003460   0x08003460   0x00000038   Code   RO          518    i.MX_USART1_UART_Init  usart.o
    0x08003498   0x08003498   0x00000038   Code   RO          519    i.MX_USART2_UART_Init  usart.o
    0x080034d0   0x080034d0   0x00000038   Code   RO          520    i.MX_USART6_UART_Init  usart.o
    0x08003508   0x08003508   0x00000002   Code   RO          613    i.MemManage_Handler  stm32f4xx_it.o
    0x0800350a   0x0800350a   0x00000072   Code   RO         4066    i.Motor_Create      motor_driver.o
    0x0800357c   0x0800357c   0x00000040   Code   RO         5122    i.Motor_Init        motor_app.o
    0x080035bc   0x080035bc   0x000000c4   Code   RO         5400    i.Motor_X_Receive_Data  stepmotor_app.o
    0x08003680   0x08003680   0x000000c4   Code   RO         5401    i.Motor_Y_Receive_Data  stepmotor_app.o
    0x08003744   0x08003744   0x00000002   Code   RO          614    i.NMI_Handler       stm32f4xx_it.o
    0x08003746   0x08003746   0x00000034   Code   RO         4793    i.OLED_Clear        oled.o
    0x0800377a   0x0800377a   0x00000002   PAD
    0x0800377c   0x0800377c   0x00000020   Code   RO         4799    i.OLED_Init         oled.o
    0x0800379c   0x0800379c   0x00000022   Code   RO         4802    i.OLED_Set_Pos      oled.o
    0x080037be   0x080037be   0x00000002   PAD
    0x080037c0   0x080037c0   0x0000009c   Code   RO         4804    i.OLED_ShowChar     oled.o
    0x0800385c   0x0800385c   0x00000058   Code   RO         4806    i.OLED_ShowString   oled.o
    0x080038b4   0x080038b4   0x00000024   Code   RO         4810    i.OLED_WR_CMD       oled.o
    0x080038d8   0x080038d8   0x00000024   Code   RO         4811    i.OLED_WR_DATA      oled.o
    0x080038fc   0x080038fc   0x00000060   Code   RO         5299    i.PID_Init          pid_app.o
    0x0800395c   0x0800395c   0x00000002   Code   RO          615    i.PendSV_Handler    stm32f4xx_it.o
    0x0800395e   0x0800395e   0x00000002   PAD
    0x08003960   0x08003960   0x00000024   Code   RO           14    i.ReadCoordinatesFromFlash  main.o
    0x08003984   0x08003984   0x00000002   Code   RO          616    i.SVC_Handler       stm32f4xx_it.o
    0x08003986   0x08003986   0x00000002   PAD
    0x08003988   0x08003988   0x00000014   Code   RO         5049    i.Scheduler_Init    scheduler.o
    0x0800399c   0x0800399c   0x0000003c   Code   RO         5050    i.Scheduler_Run     scheduler.o
    0x080039d8   0x080039d8   0x00000054   Code   RO         5403    i.StepMotor_Clear_Ready_Flags  stepmotor_app.o
    0x08003a2c   0x08003a2c   0x00000070   Code   RO         5408    i.StepMotor_Init    stepmotor_app.o
    0x08003a9c   0x08003a9c   0x00000074   Code   RO         5412    i.StepMotor_Move_Pulses_Speed  stepmotor_app.o
    0x08003b10   0x08003b10   0x00000024   Code   RO         5417    i.StepMotor_Stop    stepmotor_app.o
    0x08003b34   0x08003b34   0x00000004   Code   RO          617    i.SysTick_Handler   stm32f4xx_it.o
    0x08003b38   0x08003b38   0x00000094   Code   RO           17    i.SystemClock_Config  main.o
    0x08003bcc   0x08003bcc   0x00000010   Code   RO         4029    i.SystemInit        system_stm32f4xx.o
    0x08003bdc   0x08003bdc   0x0000002a   Code   RO         5051    i.System_Init       scheduler.o
    0x08003c06   0x08003c06   0x00000002   PAD
    0x08003c08   0x08003c08   0x000000d0   Code   RO         2787    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08003cd8   0x08003cd8   0x0000001a   Code   RO         2788    i.TIM_CCxChannelCmd  stm32f4xx_hal_tim.o
    0x08003cf2   0x08003cf2   0x00000014   Code   RO         2798    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x08003d06   0x08003d06   0x00000010   Code   RO         2799    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x08003d16   0x08003d16   0x00000002   PAD
    0x08003d18   0x08003d18   0x00000060   Code   RO         2800    i.TIM_OC1_SetConfig  stm32f4xx_hal_tim.o
    0x08003d78   0x08003d78   0x0000006c   Code   RO         2801    i.TIM_OC2_SetConfig  stm32f4xx_hal_tim.o
    0x08003de4   0x08003de4   0x00000068   Code   RO         2802    i.TIM_OC3_SetConfig  stm32f4xx_hal_tim.o
    0x08003e4c   0x08003e4c   0x00000050   Code   RO         2803    i.TIM_OC4_SetConfig  stm32f4xx_hal_tim.o
    0x08003e9c   0x08003e9c   0x00000022   Code   RO         2805    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08003ebe   0x08003ebe   0x00000024   Code   RO         2807    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08003ee2   0x08003ee2   0x00000002   PAD
    0x08003ee4   0x08003ee4   0x0000000c   Code   RO          618    i.UART4_IRQHandler  stm32f4xx_it.o
    0x08003ef0   0x08003ef0   0x0000000c   Code   RO          619    i.UART5_IRQHandler  stm32f4xx_it.o
    0x08003efc   0x08003efc   0x0000000e   Code   RO         3708    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08003f0a   0x08003f0a   0x0000004a   Code   RO         3709    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x08003f54   0x08003f54   0x00000086   Code   RO         3710    i.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x08003fda   0x08003fda   0x0000001e   Code   RO         3712    i.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x08003ff8   0x08003ff8   0x0000004e   Code   RO         3718    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08004046   0x08004046   0x0000001c   Code   RO         3719    i.UART_EndTxTransfer  stm32f4xx_hal_uart.o
    0x08004062   0x08004062   0x000000c2   Code   RO         3720    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x08004124   0x08004124   0x0000010c   Code   RO         3721    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x08004230   0x08004230   0x000000a0   Code   RO         3722    i.UART_Start_Receive_DMA  stm32f4xx_hal_uart.o
    0x080042d0   0x080042d0   0x00000036   Code   RO         3723    i.UART_Start_Receive_IT  stm32f4xx_hal_uart.o
    0x08004306   0x08004306   0x00000072   Code   RO         3724    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x08004378   0x08004378   0x0000000c   Code   RO          620    i.USART1_IRQHandler  stm32f4xx_it.o
    0x08004384   0x08004384   0x0000000c   Code   RO          621    i.USART2_IRQHandler  stm32f4xx_it.o
    0x08004390   0x08004390   0x0000000c   Code   RO          622    i.USART6_IRQHandler  stm32f4xx_it.o
    0x0800439c   0x0800439c   0x00000108   Code   RO         5331    i.Uart_Init         uart_app.o
    0x080044a4   0x080044a4   0x000001c0   Code   RO         5332    i.Uart_Task         uart_app.o
    0x08004664   0x08004664   0x00000002   Code   RO          623    i.UsageFault_Handler  stm32f4xx_it.o
    0x08004666   0x08004666   0x00000002   PAD
    0x08004668   0x08004668   0x0000002c   Code   RO         5550    i.__0vsnprintf      mc_w.l(printfa.o)
    0x08004694   0x08004694   0x00000020   Code   RO         1980    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080046b4   0x080046b4   0x0000000e   Code   RO         5618    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080046c2   0x080046c2   0x00000002   Code   RO         5619    i.__scatterload_null  mc_w.l(handlers.o)
    0x080046c4   0x080046c4   0x0000000e   Code   RO         5620    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080046d2   0x080046d2   0x00000002   PAD
    0x080046d4   0x080046d4   0x00000184   Code   RO         5552    i._fp_digits        mc_w.l(printfa.o)
    0x08004858   0x08004858   0x000006dc   Code   RO         5553    i._printf_core      mc_w.l(printfa.o)
    0x08004f34   0x08004f34   0x00000024   Code   RO         5554    i._printf_post_padding  mc_w.l(printfa.o)
    0x08004f58   0x08004f58   0x0000002e   Code   RO         5555    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08004f86   0x08004f86   0x00000016   Code   RO         5556    i._snputc           mc_w.l(printfa.o)
    0x08004f9c   0x08004f9c   0x00000026   Code   RO         4305    i.bit_array_and     ebtn.o
    0x08004fc2   0x08004fc2   0x0000001e   Code   RO         4306    i.bit_array_assign  ebtn.o
    0x08004fe0   0x08004fe0   0x0000000c   Code   RO         4307    i.bit_array_cmp     ebtn.o
    0x08004fec   0x08004fec   0x00000012   Code   RO         4308    i.bit_array_get     ebtn.o
    0x08004ffe   0x08004ffe   0x00000002   PAD
    0x08005000   0x08005000   0x00000026   Code   RO         4309    i.bit_array_or      ebtn.o
    0x08005026   0x08005026   0x00000002   PAD
    0x08005028   0x08005028   0x00000044   Code   RO         4321    i.ebtn_init         ebtn.o
    0x0800506c   0x0800506c   0x00000064   Code   RO         4325    i.ebtn_process      ebtn.o
    0x080050d0   0x080050d0   0x0000002e   Code   RO         4326    i.ebtn_process_btn  ebtn.o
    0x080050fe   0x080050fe   0x00000002   PAD
    0x08005100   0x08005100   0x000000ac   Code   RO         4327    i.ebtn_process_btn_combo  ebtn.o
    0x080051ac   0x080051ac   0x00000158   Code   RO         4328    i.ebtn_process_with_curr_state  ebtn.o
    0x08005304   0x08005304   0x0000000c   Code   RO         4330    i.ebtn_set_config   ebtn.o
    0x08005310   0x08005310   0x00000080   Code   RO           19    i.main              main.o
    0x08005390   0x08005390   0x00000034   Code   RO         4477    i.my_get_key_state  ebtn_driver.o
    0x080053c4   0x080053c4   0x00000028   Code   RO         5231    i.my_handle_key_event  key_app.o
    0x080053ec   0x080053ec   0x00000032   Code   RO          521    i.my_printf         usart.o
    0x0800541e   0x0800541e   0x00000002   PAD
    0x08005420   0x08005420   0x00000020   Code   RO         5364    i.oled_display_debug_data  oled_app.o
    0x08005440   0x08005440   0x00000030   Code   RO         4596    i.pid_init          pid.o
    0x08005470   0x08005470   0x00000014   Code   RO         4601    i.pid_set_target    pid.o
    0x08005484   0x08005484   0x0000014c   Code   RO         4331    i.prv_process_btn   ebtn.o
    0x080055d0   0x080055d0   0x00000030   Code   RO         4660    i.rt_ringbuffer_data_len  ringbuffer.o
    0x08005600   0x08005600   0x0000006e   Code   RO         4661    i.rt_ringbuffer_get  ringbuffer.o
    0x0800566e   0x0800566e   0x00000026   Code   RO         4663    i.rt_ringbuffer_init  ringbuffer.o
    0x08005694   0x08005694   0x00000072   Code   RO         4665    i.rt_ringbuffer_put  ringbuffer.o
    0x08005706   0x08005706   0x00000020   Code   RO         4670    i.rt_ringbuffer_status  ringbuffer.o
    0x08005726   0x08005726   0x00000008   Data   RO         1694    .constdata          stm32f4xx_hal_dma.o
    0x0800572e   0x0800572e   0x00000010   Data   RO         4030    .constdata          system_stm32f4xx.o
    0x0800573e   0x0800573e   0x00000008   Data   RO         4031    .constdata          system_stm32f4xx.o
    0x08005746   0x08005746   0x0000000e   Data   RO         4478    .constdata          ebtn_driver.o
    0x08005754   0x08005754   0x00000020   Data   RO         5616    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08005774, Size: 0x000026b8, Max: 0x0001c000, ABSOLUTE, COMPRESSED[0x00000580])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000008   Data   RW           22    .data               main.o
    0x20000008   COMPRESSED   0x0000000c   Data   RW         2139    .data               stm32f4xx_hal.o
    0x20000014   COMPRESSED   0x00000004   Data   RW         4032    .data               system_stm32f4xx.o
    0x20000018   COMPRESSED   0x00000001   Data   RW         4281    .data               led_driver.o
    0x20000019   COMPRESSED   0x00000003   PAD
    0x2000001c   COMPRESSED   0x0000001c   Data   RW         4479    .data               ebtn_driver.o
    0x20000038   COMPRESSED   0x0000081f   Data   RW         4814    .data               oled.o
    0x20000857   COMPRESSED   0x00000001   PAD
    0x20000858   COMPRESSED   0x00000040   Data   RW         5052    .data               scheduler.o
    0x20000898   COMPRESSED   0x00000004   Data   RW         5194    .data               led_app.o
    0x2000089c   COMPRESSED   0x00000028   Data   RW         5302    .data               pid_app.o
    0x200008c4   COMPRESSED   0x0000001c   Data   RW         5419    .data               stepmotor_app.o
    0x200008e0        -       0x00000044   Zero   RW           20    .bss                main.o
    0x20000924        -       0x00000054   Zero   RW          360    .bss                i2c.o
    0x20000978        -       0x00000058   Zero   RW          402    .bss                spi.o
    0x200009d0        -       0x000000d8   Zero   RW          449    .bss                tim.o
    0x20000aa8        -       0x00000348   Zero   RW          522    .bss                usart.o
    0x20000df0        -       0x00000034   Zero   RW         4332    .bss                ebtn.o
    0x20000e24        -       0x000002bc   Zero   RW         4748    .bss                uart_driver.o
    0x200010e0        -       0x00000080   Zero   RW         4749    .bss                uart_driver.o
    0x20001160        -       0x00000080   Zero   RW         4750    .bss                uart_driver.o
    0x200011e0        -       0x00000080   Zero   RW         4751    .bss                uart_driver.o
    0x20001260        -       0x00000080   Zero   RW         4752    .bss                uart_driver.o
    0x200012e0        -       0x00000080   Zero   RW         4753    .bss                uart_driver.o
    0x20001360        -       0x00000080   Zero   RW         4754    .bss                uart_driver.o
    0x200013e0        -       0x00000080   Zero   RW         4755    .bss                uart_driver.o
    0x20001460        -       0x00000080   Zero   RW         4756    .bss                uart_driver.o
    0x200014e0        -       0x00000080   Zero   RW         4757    .bss                uart_driver.o
    0x20001560        -       0x00000080   Zero   RW         4758    .bss                uart_driver.o
    0x200015e0        -       0x00000028   Zero   RW         5123    .bss                motor_app.o
    0x20001608        -       0x00000020   Zero   RW         5158    .bss                encoder_app.o
    0x20001628        -       0x00000078   Zero   RW         5300    .bss                pid_app.o
    0x200016a0        -       0x00000014   Zero   RW         5418    .bss                stepmotor_app.o
    0x200016b4   COMPRESSED   0x00000004   PAD
    0x200016b8        -       0x00001000   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x08005cf4, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       124          4          0          0          0        882   dma.o
      1210         22          0          0         52      18195   ebtn.o
       104         18         14         28          0       2007   ebtn_driver.o
       220          0          0          0          0       3663   emm_v5.o
        64         16          0          0         32       1915   encoder_app.o
       128         18          0          0          0       2355   encoder_driver.o
       288         22          0          0          0       1127   gpio.o
       498         24          0          0          0       7042   hwt101_driver.o
       172         28          0          0         84       1757   i2c.o
        58          6          0          0          0       1816   key_app.o
        24          8          0          4          0       1400   led_app.o
       144         12          0          1          0        758   led_driver.o
       316         52          0          8         68     721149   main.o
        64         14          0          0         40        758   motor_app.o
       114          0          0          0          0       2055   motor_driver.o
       434         20          0       2079          0       5779   oled.o
        32         14          0          0          0        802   oled_app.o
        68          6          0          0          0       2312   pid.o
        96         12          0         40        120       1059   pid_app.o
       342          0          0          0          0       6294   ringbuffer.o
       122         10          0         64          0       2682   scheduler.o
       176         22          0          0         88       1769   spi.o
        36          8        392          0       4096        856   startup_stm32f407xx.o
       740        220          0         28         20       5344   stepmotor_app.o
       180         28          0         12          0       9569   stm32f4xx_hal.o
       198         14          0          0          0      33943   stm32f4xx_hal_cortex.o
      1084         16          8          0          0       7526   stm32f4xx_hal_dma.o
       516         46          0          0          0       2931   stm32f4xx_hal_gpio.o
      1414         32          0          0          0       9819   stm32f4xx_hal_i2c.o
        48          6          0          0          0        898   stm32f4xx_hal_msp.o
      1344         72          0          0          0       5380   stm32f4xx_hal_rcc.o
       188          0          0          0          0       1255   stm32f4xx_hal_spi.o
      1840        108          0          0          0      15411   stm32f4xx_hal_tim.o
       228         28          0          0          0       2256   stm32f4xx_hal_tim_ex.o
      2268         28          0          0          0      17052   stm32f4xx_hal_uart.o
       140         60          0          0          0       9274   stm32f4xx_it.o
        16          4         24          4          0       1187   system_stm32f4xx.o
       720         74          0          0        216       4700   tim.o
       712        252          0          0          0       3066   uart_app.o
       320         72          0          0       1980       2781   uart_driver.o
      1174        114          0          0        840       8437   usart.o

    ----------------------------------------------------------------------
     18010       <USER>        <GROUP>       2272       7640     929261   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        46          0          0          4          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        86          0          0          0          0          0   __dczerorl2.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        26          0          0          0          0         80   memcmp.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2292         84          0          0          0        516   printfa.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o

    ----------------------------------------------------------------------
      3908        <USER>          <GROUP>          0          0       1940   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2802        100          0          0          0       1216   mc_w.l
      1104          0          0          0          0        724   mf_w.l

    ----------------------------------------------------------------------
      3908        <USER>          <GROUP>          0          0       1940   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     21918       1610        470       2272       7640     913197   Grand Totals
     21918       1610        470       1408       7640     913197   ELF Image Totals (compressed)
     21918       1610        470       1408          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                22388 (  21.86kB)
    Total RW  Size (RW Data + ZI Data)              9912 (   9.68kB)
    Total ROM Size (Code + RO Data + RW Data)      23796 (  23.24kB)

==============================================================================

